<?php
/**
 * 网络验证系统配置文件
 */

// MySQL数据库配置
define('DB_HOST', 'localhost');
define('DB_PORT', 3306);
define('DB_NAME', '218_93_208_123_8');
define('DB_USER', '218_93_208_123_8');
define('DB_PASS', 'd13f1T1SeYwf5XnF');
define('DB_CHARSET', 'utf8mb4');

// 服务器配置
define('SERVER_HOST', '**************');
define('SERVER_PORT', 8742);
define('BASE_URL', 'http://**************:8742');

// 加密配置
define('DES_KEY', 'NETAUTH2025');

// 安全配置
define('MAX_LOGIN_ATTEMPTS', 5);
define('SESSION_TIMEOUT', 3600); // 1小时
define('REQUEST_TIMEOUT', 300);  // 5分钟

// API配置
define('API_VERSION', '1.0');
define('ALLOW_ORIGINS', '*');

// 日志配置
define('LOG_PATH', __DIR__ . '/logs/');
define('LOG_LEVEL', 'INFO');

// 系统配置
define('TIMEZONE', 'Asia/Shanghai');
date_default_timezone_set(TIMEZONE);

// 软件标识和版本配置
define('SOFTWARE_ID', '123456789012345678'); // 18位软件标识
define('SOFTWARE_VERSION', '1.0.0'); // 软件版本号

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', LOG_PATH . 'error.log');

// 创建日志目录
if (!is_dir(LOG_PATH)) {
    mkdir(LOG_PATH, 0755, true);
}

/**
 * 记录日志
 */
function 记录日志($级别, $消息, $上下文 = []) {
    $时间戳 = date('Y-m-d H:i:s');
    $日志内容 = "[{$时间戳}] [{$级别}] {$消息}";
    
    if (!empty($上下文)) {
        $日志内容 .= ' ' . json_encode($上下文, JSON_UNESCAPED_UNICODE);
    }
    
    $日志内容 .= PHP_EOL;
    
    $日志文件 = LOG_PATH . date('Y-m-d') . '.log';
    file_put_contents($日志文件, $日志内容, FILE_APPEND | LOCK_EX);
}

/**
 * 获取配置值
 */
function 获取配置($键, $默认值 = null) {
    $配置映射 = [
        'db_host' => DB_HOST,
        'db_port' => DB_PORT,
        'db_name' => DB_NAME,
        'db_user' => DB_USER,
        'db_pass' => DB_PASS,
        'db_charset' => DB_CHARSET,
        'server_host' => SERVER_HOST,
        'server_port' => SERVER_PORT,
        'base_url' => BASE_URL,
        'des_key' => DES_KEY,
        'max_login_attempts' => MAX_LOGIN_ATTEMPTS,
        'session_timeout' => SESSION_TIMEOUT,
        'request_timeout' => REQUEST_TIMEOUT,
        'api_version' => API_VERSION,
        'software_id' => SOFTWARE_ID,
        'software_version' => SOFTWARE_VERSION
    ];

    return $配置映射[$键] ?? $默认值;
}
?>

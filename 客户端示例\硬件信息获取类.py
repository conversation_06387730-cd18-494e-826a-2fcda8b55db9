#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硬件信息获取类
获取计算机硬件信息用于设备识别和绑定
"""

import platform
import socket
import subprocess
import hashlib
import uuid
import psutil
import requests
import re
import os


class 硬件信息获取类:
    def __init__(self):
        self.系统类型 = platform.system()
    
    def 获取CPU序列号(self):
        """
        获取CPU序列号
        :return: CPU序列号字符串
        """
        try:
            if self.系统类型 == "Windows":
                # Windows系统使用wmic命令
                result = subprocess.run(
                    ['wmic', 'cpu', 'get', 'ProcessorId', '/value'],
                    capture_output=True,
                    text=True,
                    encoding='gbk'
                )
                for line in result.stdout.split('\n'):
                    if 'ProcessorId=' in line:
                        cpu_id = line.split('=')[1].strip()
                        if cpu_id:
                            return cpu_id
            
            elif self.系统类型 == "Linux":
                # Linux系统读取/proc/cpuinfo
                try:
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if 'Serial' in line:
                                return line.split(':')[1].strip()
                except:
                    pass
                
                # 备用方案：使用dmidecode
                try:
                    result = subprocess.run(
                        ['dmidecode', '-t', 'processor'],
                        capture_output=True,
                        text=True
                    )
                    for line in result.stdout.split('\n'):
                        if 'ID:' in line:
                            return line.split(':')[1].strip()
                except:
                    pass
            
            elif self.系统类型 == "Darwin":  # macOS
                try:
                    result = subprocess.run(
                        ['system_profiler', 'SPHardwareDataType'],
                        capture_output=True,
                        text=True
                    )
                    for line in result.stdout.split('\n'):
                        if 'Serial Number' in line:
                            return line.split(':')[1].strip()
                except:
                    pass
            
            # 如果无法获取真实CPU序列号，生成基于硬件的唯一标识
            return self.生成硬件指纹()
            
        except Exception as e:
            print(f"获取CPU序列号失败: {e}")
            return self.生成硬件指纹()
    
    def 获取主板序列号(self):
        """
        获取主板序列号
        :return: 主板序列号字符串
        """
        try:
            if self.系统类型 == "Windows":
                result = subprocess.run(
                    ['wmic', 'baseboard', 'get', 'SerialNumber', '/value'],
                    capture_output=True,
                    text=True,
                    encoding='gbk'
                )
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        serial = line.split('=')[1].strip()
                        if serial and serial != 'To be filled by O.E.M.':
                            return serial
            
            elif self.系统类型 == "Linux":
                try:
                    result = subprocess.run(
                        ['dmidecode', '-s', 'baseboard-serial-number'],
                        capture_output=True,
                        text=True
                    )
                    serial = result.stdout.strip()
                    if serial and serial != 'To be filled by O.E.M.':
                        return serial
                except:
                    pass
            
            return "UNKNOWN_BOARD"
            
        except Exception as e:
            print(f"获取主板序列号失败: {e}")
            return "UNKNOWN_BOARD"
    
    def 获取硬盘序列号(self):
        """
        获取硬盘序列号
        :return: 硬盘序列号字符串
        """
        try:
            if self.系统类型 == "Windows":
                result = subprocess.run(
                    ['wmic', 'diskdrive', 'get', 'SerialNumber', '/value'],
                    capture_output=True,
                    text=True,
                    encoding='gbk'
                )
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        serial = line.split('=')[1].strip()
                        if serial:
                            return serial
            
            elif self.系统类型 == "Linux":
                try:
                    # 尝试获取第一个硬盘的序列号
                    result = subprocess.run(
                        ['lsblk', '-d', '-o', 'name,serial'],
                        capture_output=True,
                        text=True
                    )
                    lines = result.stdout.split('\n')[1:]  # 跳过标题行
                    for line in lines:
                        if line.strip():
                            parts = line.split()
                            if len(parts) >= 2 and parts[1] != '':
                                return parts[1]
                except:
                    pass
            
            return "UNKNOWN_DISK"
            
        except Exception as e:
            print(f"获取硬盘序列号失败: {e}")
            return "UNKNOWN_DISK"
    
    def 获取MAC地址(self):
        """
        获取网卡MAC地址
        :return: MAC地址字符串
        """
        try:
            # 获取第一个有效的MAC地址
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                           for elements in range(0, 2*6, 2)][::-1])
            return mac.upper()
        except Exception as e:
            print(f"获取MAC地址失败: {e}")
            return "UNKNOWN_MAC"
    
    def 获取本地IP(self):
        """
        获取本地IP地址
        :return: IP地址字符串
        """
        try:
            # 连接到外部地址获取本地IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception as e:
            try:
                # 备用方案：获取主机名对应的IP
                return socket.gethostbyname(socket.gethostname())
            except:
                return "127.0.0.1"
    
    def 获取公网IP(self):
        """
        获取公网IP地址
        :return: 公网IP地址字符串
        """
        try:
            # 尝试多个IP查询服务
            服务列表 = [
                'https://api.ipify.org',
                'https://icanhazip.com',
                'https://ident.me',
                'http://whatismyip.akamai.com'
            ]
            
            for 服务 in 服务列表:
                try:
                    response = requests.get(服务, timeout=5)
                    if response.status_code == 200:
                        ip = response.text.strip()
                        # 验证IP格式
                        if re.match(r'^(\d{1,3}\.){3}\d{1,3}$', ip):
                            return ip
                except:
                    continue
            
            return self.获取本地IP()
            
        except Exception as e:
            print(f"获取公网IP失败: {e}")
            return self.获取本地IP()
    
    def 生成硬件指纹(self):
        """
        生成基于多个硬件信息的唯一指纹
        :return: 硬件指纹字符串
        """
        try:
            # 收集多种硬件信息
            信息列表 = [
                platform.machine(),
                platform.processor(),
                str(psutil.cpu_count()),
                str(psutil.virtual_memory().total),
                self.获取MAC地址(),
                platform.node(),
                str(uuid.getnode())
            ]
            
            # 添加系统特定信息
            if self.系统类型 == "Windows":
                try:
                    result = subprocess.run(
                        ['wmic', 'computersystem', 'get', 'TotalPhysicalMemory', '/value'],
                        capture_output=True,
                        text=True,
                        encoding='gbk'
                    )
                    信息列表.append(result.stdout)
                except:
                    pass
            
            # 生成MD5哈希
            硬件字符串 = '|'.join(信息列表)
            return hashlib.md5(硬件字符串.encode('utf-8')).hexdigest().upper()
            
        except Exception as e:
            print(f"生成硬件指纹失败: {e}")
            return hashlib.md5(str(uuid.getnode()).encode('utf-8')).hexdigest().upper()
    
    def 获取机器码(self):
        """
        获取综合机器码
        :return: 机器码字符串
        """
        try:
            # 组合多个硬件标识
            组件列表 = [
                self.获取CPU序列号(),
                self.获取主板序列号(),
                self.获取硬盘序列号(),
                self.获取MAC地址(),
                platform.machine(),
                str(psutil.virtual_memory().total)
            ]
            
            # 过滤空值和默认值
            有效组件 = []
            for 组件 in 组件列表:
                if 组件 and 组件 not in ['UNKNOWN_BOARD', 'UNKNOWN_DISK', 'To be filled by O.E.M.']:
                    有效组件.append(组件)
            
            # 生成最终机器码
            机器码字符串 = '|'.join(有效组件)
            return hashlib.sha256(机器码字符串.encode('utf-8')).hexdigest().upper()[:32]
            
        except Exception as e:
            print(f"获取机器码失败: {e}")
            return self.生成硬件指纹()[:32]
    
    def 获取系统信息(self):
        """
        获取完整的系统信息
        :return: 系统信息字典
        """
        return {
            'cpu_serial': self.获取CPU序列号(),
            'board_serial': self.获取主板序列号(),
            'disk_serial': self.获取硬盘序列号(),
            'mac_address': self.获取MAC地址(),
            'local_ip': self.获取本地IP(),
            'public_ip': self.获取公网IP(),
            'machine_code': self.获取机器码(),
            'hardware_fingerprint': self.生成硬件指纹(),
            'system_info': {
                'platform': platform.platform(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'hostname': platform.node(),
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total
            }
        }
    
    def 测试硬件信息(self):
        """
        测试硬件信息获取功能
        """
        print("=== 硬件信息测试 ===")
        信息 = self.获取系统信息()
        
        for 键, 值 in 信息.items():
            if isinstance(值, dict):
                print(f"{键}:")
                for 子键, 子值 in 值.items():
                    print(f"  {子键}: {子值}")
            else:
                print(f"{键}: {值}")


if __name__ == '__main__':
    # 运行测试
    硬件工具 = 硬件信息获取类()
    硬件工具.测试硬件信息()

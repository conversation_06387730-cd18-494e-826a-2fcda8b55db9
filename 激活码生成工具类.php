<?php
/**
 * 激活码生成工具类
 * 生成18位STEAM格式激活码
 */

class 激活码生成工具类 {
    private $前缀 = 'STEAM';
    private $字符集 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    private $随机部分长度 = 13;
    
    /**
     * 生成单个激活码
     * @return string 18位激活码
     */
    public function 生成激活码() {
        $随机部分 = '';
        $字符集长度 = strlen($this->字符集);
        
        for ($i = 0; $i < $this->随机部分长度; $i++) {
            $随机部分 .= $this->字符集[random_int(0, $字符集长度 - 1)];
        }
        
        return $this->前缀 . $随机部分;
    }
    
    /**
     * 批量生成激活码
     * @param int $数量 生成数量
     * @param array $已存在激活码 已存在的激活码数组，用于去重
     * @return array 激活码数组
     */
    public function 批量生成激活码($数量, $已存在激活码 = []) {
        $激活码列表 = [];
        $已存在集合 = array_flip($已存在激活码);
        $尝试次数 = 0;
        $最大尝试次数 = $数量 * 10; // 防止无限循环
        
        while (count($激活码列表) < $数量 && $尝试次数 < $最大尝试次数) {
            $新激活码 = $this->生成激活码();
            
            // 检查是否重复
            if (!isset($已存在集合[$新激活码]) && !in_array($新激活码, $激活码列表)) {
                $激活码列表[] = $新激活码;
                $已存在集合[$新激活码] = true;
            }
            
            $尝试次数++;
        }
        
        if (count($激活码列表) < $数量) {
            throw new Exception("生成激活码失败，可能存在重复冲突");
        }
        
        return $激活码列表;
    }
    
    /**
     * 验证激活码格式
     * @param string $激活码 要验证的激活码
     * @return bool 是否符合格式
     */
    public function 验证激活码格式($激活码) {
        // 检查长度
        if (strlen($激活码) !== 18) {
            return false;
        }
        
        // 检查前缀
        if (substr($激活码, 0, 5) !== $this->前缀) {
            return false;
        }
        
        // 检查随机部分字符
        $随机部分 = substr($激活码, 5);
        for ($i = 0; $i < strlen($随机部分); $i++) {
            if (strpos($this->字符集, $随机部分[$i]) === false) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 生成激活码数据结构
     * @param int $数量 生成数量
     * @param int $初始点数 初始点数
     * @param string $备注 备注
     * @param array $已存在激活码 已存在的激活码
     * @return array 激活码数据数组
     */
    public function 生成激活码数据($数量, $初始点数 = 9999, $备注 = '', $已存在激活码 = []) {
        $激活码列表 = $this->批量生成激活码($数量, $已存在激活码);
        $激活码数据 = [];

        foreach ($激活码列表 as $激活码) {
            $激活码数据[] = [
                '激活码' => $激活码,
                '激活码状态' => 0, // 未使用
                '初始点数' => $初始点数,
                '剩余点数' => $初始点数,
                '每次扣除点数' => 1,
                '每日解绑次数' => 0,
                '最大解绑次数' => 3,
                '云数据' => null,
                '备注' => $备注,
                '创建时间' => date('Y-m-d H:i:s')
            ];
        }

        return $激活码数据;
    }
    
    /**
     * 导出激活码为文本格式
     * @param array $激活码数据 激活码数据数组
     * @param string $格式 导出格式: txt, csv, json
     * @return string 导出内容
     */
    public function 导出激活码($激活码数据, $格式 = 'txt') {
        switch (strtolower($格式)) {
            case 'txt':
                return $this->导出为TXT($激活码数据);
            case 'csv':
                return $this->导出为CSV($激活码数据);
            case 'json':
                return $this->导出为JSON($激活码数据);
            default:
                throw new Exception("不支持的导出格式: {$格式}");
        }
    }
    
    /**
     * 导出为TXT格式
     */
    private function 导出为TXT($激活码数据) {
        $内容 = "激活码列表 - 生成时间: " . date('Y-m-d H:i:s') . "\n";
        $内容 .= str_repeat("=", 50) . "\n\n";

        foreach ($激活码数据 as $index => $数据) {
            $内容 .= sprintf("%d. %s (点数: %d)\n",
                $index + 1,
                $数据['激活码'],
                $数据['初始点数']
            );
        }

        $内容 .= "\n总计: " . count($激活码数据) . " 个激活码\n";
        return $内容;
    }
    
    /**
     * 导出为CSV格式
     */
    private function 导出为CSV($激活码数据) {
        $内容 = "激活码,初始点数,剩余点数,状态,备注\n";

        foreach ($激活码数据 as $数据) {
            $状态文本 = $数据['激活码状态'] == 0 ? '未使用' : '已使用';
            $内容 .= sprintf('"%s",%d,%d,"%s","%s"' . "\n",
                $数据['激活码'],
                $数据['初始点数'],
                $数据['剩余点数'],
                $状态文本,
                $数据['备注']
            );
        }
        
        return $内容;
    }
    
    /**
     * 导出为JSON格式
     */
    private function 导出为JSON($激活码数据) {
        $导出数据 = [
            'export_time' => date('Y-m-d H:i:s'),
            'total_count' => count($激活码数据),
            'activation_codes' => $激活码数据
        ];
        
        return json_encode($导出数据, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * 统计激活码信息
     * @param array $激活码数据 激活码数据数组
     * @return array 统计信息
     */
    public function 统计激活码信息($激活码数据) {
        $总数量 = count($激活码数据);
        $总点数 = array_sum(array_column($激活码数据, '初始点数'));
        $平均点数 = $总数量 > 0 ? round($总点数 / $总数量, 2) : 0;
        
        $点数分布 = [];
        foreach ($激活码数据 as $数据) {
            $点数 = $数据['初始点数'];
            $点数分布[$点数] = ($点数分布[$点数] ?? 0) + 1;
        }
        
        return [
            '总数量' => $总数量,
            '总点数' => $总点数,
            '平均点数' => $平均点数,
            '点数分布' => $点数分布,
            '生成时间' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 设置激活码前缀
     * @param string $前缀 新前缀
     */
    public function 设置前缀($前缀) {
        if (strlen($前缀) > 10) {
            throw new Exception("前缀长度不能超过10个字符");
        }
        $this->前缀 = strtoupper($前缀);
        $this->随机部分长度 = 18 - strlen($this->前缀);
        
        if ($this->随机部分长度 < 5) {
            throw new Exception("前缀过长，随机部分至少需要5个字符");
        }
    }
    
    /**
     * 获取当前配置
     * @return array 配置信息
     */
    public function 获取配置() {
        return [
            '前缀' => $this->前缀,
            '随机部分长度' => $this->随机部分长度,
            '总长度' => strlen($this->前缀) + $this->随机部分长度,
            '字符集' => $this->字符集,
            '字符集长度' => strlen($this->字符集)
        ];
    }
}
?>

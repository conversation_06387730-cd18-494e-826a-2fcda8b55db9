# 网络验证系统 API 文档

## 📋 系统概述

本网络验证系统基于激活码机制，支持点数计费、设备绑定、云数据存储、封禁管理等功能。系统采用DES加密通信，确保数据传输安全。

### 🔧 技术特性
- **加密通信**: DES加密 + 签名验证
- **设备绑定**: 基于机器码的硬件绑定
- **点数计费**: 灵活的点数扣除机制
- **云数据**: 支持纯文本云数据存储
- **地理位置**: IP地理位置跟踪
- **防破解**: 心跳检测、状态验证

---

## 🌐 服务器信息

- **服务器地址**: `http://**************:8742`
- **客户端API**: `/新版网络验证API.php`
- **管理员API**: `/新版管理员API.php`
- **请求方式**: `POST`
- **数据格式**: `JSON`
- **编码方式**: `UTF-8`

---

## 🔐 加密通信机制

### 请求格式
```json
{
    "encrypted_data": "加密后的数据",
    "timestamp": 1640995200
}
```

### 加密数据内容
```json
{
    "action": "操作类型",
    "参数1": "值1",
    "参数2": "值2",
    "timestamp": 1640995200,
    "sign": "签名"
}
```

### 响应格式
```json
{
    "encrypted_data": "加密后的响应数据",
    "timestamp": 1640995200
}
```

---

## 👤 客户端API接口

### 1. 用户登录 `login`

**功能**: 激活码登录验证

**请求参数**:
- `action`: `"login"` (必需)
- `auth_code`: 激活码 (必需)
- `machine_code`: 机器码 (必需)
- `cpu_serial`: CPU序列号 (可选)

**示例请求**:
```json
{
    "action": "login",
    "auth_code": "STEAM123456789012345",
    "machine_code": "ABC123DEF456GHI789",
    "cpu_serial": "BFEBFBFF000906EA"
}
```

**成功响应**:
```json
{
    "status": "success",
    "message": "登录成功",
    "user_info": {
        "auth_code": "STEAM123456789012345",
        "login_time": "2024-01-01 12:00:00",
        "remaining_points": 99,
        "total_logins": 1
    },
    "session_token": "abc123def456...",
    "points_info": {
        "before_points": 100,
        "deducted_points": 1,
        "after_points": 99
    }
}
```

**错误响应**:
```json
{
    "status": "error",
    "message": "激活码无效或已过期"
}
```

### 2. 状态验证 `verify`

**功能**: 验证当前登录状态

**请求参数**:
- `action`: `"verify"` (必需)
- `auth_code`: 激活码 (必需)
- `session_token`: 会话令牌 (必需)

**示例请求**:
```json
{
    "action": "verify",
    "auth_code": "STEAM123456789012345",
    "session_token": "abc123def456..."
}
```

**成功响应**:
```json
{
    "status": "valid",
    "message": "状态有效",
    "remaining_points": 99,
    "server_time": "2024-01-01 12:05:00"
}
```

### 3. 心跳检测 `heartbeat`

**功能**: 防破解心跳检测，实时验证激活码状态

**安全检查**:
- ✅ 激活码封禁状态检查
- ✅ IP地址封禁状态检查
- ✅ 激活码有效性验证
- ✅ 剩余点数检查
- ✅ 设备绑定验证（可选）

**请求参数**:
- `action`: `"heartbeat"` (必需)
- `auth_code`: 激活码 (必需)
- `machine_code`: 机器码 (可选，用于设备绑定验证)

**示例请求**:
```json
{
    "action": "heartbeat",
    "auth_code": "STEAM123456789012345",
    "machine_code": "MACHINE_CODE_123456"
}
```

**成功响应**:
```json
{
    "status": "alive",
    "message": "心跳正常",
    "server_time": "2024-01-01 12:05:00",
    "remaining_points": 98
}
```

**失败响应示例**:

1. **激活码被封禁**:
```json
{
    "status": "error",
    "message": "激活码已被封禁: 违规使用"
}
```

2. **IP地址被封禁**:
```json
{
    "status": "error",
    "message": "IP地址已被封禁: 恶意行为"
}
```

3. **激活码无效**:
```json
{
    "status": "error",
    "message": "激活码无效或已过期"
}
```

4. **点数不足**:
```json
{
    "status": "error",
    "message": "点数不足，心跳检测失败"
}
```

5. **设备不匹配**:
```json
{
    "status": "error",
    "message": "设备不匹配，心跳检测失败"
}
```

**使用建议**:
- 建议客户端每30-60秒发送一次心跳请求
- 心跳失败时应立即停止程序运行
- 可配合状态验证API使用，提供双重保护

### 4. 查询点数 `query_points`

**功能**: 查询激活码剩余点数

**请求参数**:
- `action`: `"query_points"` (必需)
- `auth_code`: 激活码 (必需)

**示例请求**:
```json
{
    "action": "query_points",
    "auth_code": "STEAM123456789012345"
}
```

**成功响应**:
```json
{
    "status": "success",
    "remaining_points": 99,
    "initial_points": 100,
    "total_used": 1
}
```

### 5. 设备解绑 `unbind_device`

**功能**: 用户自助解绑设备

**请求参数**:
- `action`: `"unbind_device"` (必需)
- `auth_code`: 激活码 (必需)

**示例请求**:
```json
{
    "action": "unbind_device",
    "auth_code": "STEAM123456789012345"
}
```

**成功响应**:
```json
{
    "status": "success",
    "message": "设备解绑成功",
    "unbind_info": {
        "remaining_unbinds": 2
    }
}
```

### 6. 查询解绑信息 `query_unbind_info`

**功能**: 查询解绑次数限制信息

**请求参数**:
- `action`: `"query_unbind_info"` (必需)
- `auth_code`: 激活码 (必需)

**示例请求**:
```json
{
    "action": "query_unbind_info",
    "auth_code": "STEAM123456789012345"
}
```

**成功响应**:
```json
{
    "status": "success",
    "message": "查询成功",
    "unbind_info": {
        "today_unbind_count": 1,
        "max_unbind_per_day": 3,
        "remaining_unbinds": 2,
        "allowed": true,
        "reset_date": "2024-01-01"
    }
}
```

### 7. 设置云数据 `set_cloud_data`

**功能**: 保存用户云数据

**请求参数**:
- `action`: `"set_cloud_data"` (必需)
- `auth_code`: 激活码 (必需)
- `cloud_data`: 云数据内容 (必需，纯文本格式)

**示例请求**:
```json
{
    "action": "set_cloud_data",
    "auth_code": "STEAM123456789012345",
    "cloud_data": "用户配置数据或游戏存档"
}
```

**成功响应**:
```json
{
    "status": "success",
    "message": "云数据保存成功"
}
```

### 8. 获取云数据 `get_cloud_data`

**功能**: 获取用户云数据

**请求参数**:
- `action`: `"get_cloud_data"` (必需)
- `auth_code`: 激活码 (必需)

**示例请求**:
```json
{
    "action": "get_cloud_data",
    "auth_code": "STEAM123456789012345"
}
```

**成功响应**:
```json
{
    "status": "success",
    "cloud_data": "用户配置数据或游戏存档"
}
```

### 9. 用户登出 `logout`

**功能**: 用户登出

**请求参数**:
- `action`: `"logout"` (必需)
- `auth_code`: 激活码 (必需)

**示例请求**:
```json
{
    "action": "logout",
    "auth_code": "STEAM123456789012345"
}
```

**成功响应**:
```json
{
    "status": "success",
    "message": "登出成功"
}
```

---

## 🔧 管理员API接口

### 认证要求
所有管理员API请求都需要包含管理员密码：
```json
{
    "action": "操作类型",
    "admin_password": "admin123456",
    "其他参数": "值"
}
```

### 1. 获取激活码列表 `get_auth_codes`

**功能**: 获取所有激活码信息

**请求参数**:
- `action`: `"get_auth_codes"` (必需)
- `admin_password`: 管理员密码 (必需)
- `limit`: 返回数量限制 (可选，默认100)
- `status_filter`: 状态过滤 (可选)

**示例请求**:
```json
{
    "action": "get_auth_codes",
    "admin_password": "admin123456",
    "limit": 50
}
```

### 2. 生成激活码 `generate_auth_codes`

**功能**: 批量生成激活码

**请求参数**:
- `action`: `"generate_auth_codes"` (必需)
- `admin_password`: 管理员密码 (必需)
- `count`: 生成数量 (必需)
- `points`: 初始点数 (必需)
- `prefix`: 激活码前缀 (可选，默认"STEAM")

**示例请求**:
```json
{
    "action": "generate_auth_codes",
    "admin_password": "admin123456",
    "count": 10,
    "points": 100,
    "prefix": "STEAM"
}
```

### 3. 修改点数 `modify_points`

**功能**: 修改激活码点数

**请求参数**:
- `action`: `"modify_points"` (必需)
- `admin_password`: 管理员密码 (必需)
- `auth_code`: 激活码 (必需)
- `operation`: 操作类型 "add"或"subtract" (必需)
- `points`: 点数 (必需)
- `reason`: 操作原因 (可选)

**示例请求**:
```json
{
    "action": "modify_points",
    "admin_password": "admin123456",
    "auth_code": "STEAM123456789012345",
    "operation": "add",
    "points": 50,
    "reason": "管理员充值"
}
```

### 4. 封禁目标 `ban_target`

**功能**: 封禁激活码、IP或机器码

**请求参数**:
- `action`: `"ban_target"` (必需)
- `admin_password`: 管理员密码 (必需)
- `target_type`: 目标类型 "auth_code"/"ip"/"machine_code" (必需)
- `target_value`: 目标值 (必需)
- `reason`: 封禁原因 (必需)
- `duration`: 封禁时长(天) (可选，0为永久)

**示例请求**:
```json
{
    "action": "ban_target",
    "admin_password": "admin123456",
    "target_type": "auth_code",
    "target_value": "STEAM123456789012345",
    "reason": "违规使用",
    "duration": 7
}
```

### 5. 解除封禁 `unban_target`

**功能**: 解除目标封禁

**请求参数**:
- `action`: `"unban_target"` (必需)
- `admin_password`: 管理员密码 (必需)
- `target_type`: 目标类型 (必需)
- `target_value`: 目标值 (必需)

### 6. 获取封禁列表 `get_ban_list`

**功能**: 获取所有封禁记录

**请求参数**:
- `action`: `"get_ban_list"` (必需)
- `admin_password`: 管理员密码 (必需)

### 7. 获取系统统计 `get_system_stats`

**功能**: 获取系统统计信息

**请求参数**:
- `action`: `"get_system_stats"` (必需)
- `admin_password`: 管理员密码 (必需)

**成功响应**:
```json
{
    "status": "success",
    "data": {
        "total_auth_codes": 100,
        "active_auth_codes": 85,
        "total_logins": 1250,
        "today_logins": 45,
        "banned_count": 5
    }
}
```

### 8. 获取登录日志 `get_login_logs`

**功能**: 获取登录记录

**请求参数**:
- `action`: `"get_login_logs"` (必需)
- `admin_password`: 管理员密码 (必需)
- `limit`: 返回数量 (可选，默认100)
- `auth_code`: 筛选激活码 (可选)

### 9. 获取点数记录 `get_points_logs`

**功能**: 获取点数变动记录

**请求参数**:
- `action`: `"get_points_logs"` (必需)
- `admin_password`: 管理员密码 (必需)
- `auth_code`: 激活码 (可选)

### 10. 获取IP地理位置统计 `get_ip_stats`

**功能**: 获取IP地理位置统计

**请求参数**:
- `action`: `"get_ip_stats"` (必需)
- `admin_password`: 管理员密码 (必需)

### 11. 管理云数据 `manage_cloud_data`

**功能**: 管理员云数据操作

**请求参数**:
- `action`: `"manage_cloud_data"` (必需)
- `admin_password`: 管理员密码 (必需)
- `auth_code`: 激活码 (必需)
- `operation`: 操作类型 "get"/"set"/"clear" (必需)
- `cloud_data`: 云数据内容 (set操作时必需)

**示例请求(设置)**:
```json
{
    "action": "manage_cloud_data",
    "admin_password": "admin123456",
    "auth_code": "STEAM123456789012345",
    "operation": "set",
    "cloud_data": "管理员设置的数据"
}
```

### 12. 重置解绑次数 `reset_unbind_count`

**功能**: 重置激活码解绑次数

**请求参数**:
- `action`: `"reset_unbind_count"` (必需)
- `admin_password`: 管理员密码 (必需)
- `auth_code`: 激活码 (必需)

### 13. 管理员解绑设备 `admin_unbind_device`

**功能**: 管理员强制解绑设备

**请求参数**:
- `action`: `"admin_unbind_device"` (必需)
- `admin_password`: 管理员密码 (必需)
- `auth_code`: 激活码 (必需)

### 14. 获取激活码详细信息 `get_auth_code_detail`

**功能**: 获取单个激活码详细信息

**请求参数**:
- `action`: `"get_auth_code_detail"` (必需)
- `admin_password`: 管理员密码 (必需)
- `auth_code`: 激活码 (必需)

### 15. 删除激活码 `delete_auth_code`

**功能**: 删除激活码

**请求参数**:
- `action`: `"delete_auth_code"` (必需)
- `admin_password`: 管理员密码 (必需)
- `auth_code`: 激活码 (必需)

### 16. 修改解绑次数限制 `modify_unbind_limit`

**功能**: 修改解绑次数限制

**请求参数**:
- `action`: `"modify_unbind_limit"` (必需)
- `admin_password`: 管理员密码 (必需)
- `auth_code`: 激活码 (必需)
- `new_limit`: 新的解绑次数限制 (必需)

### 17. 设置云数据 `set_cloud_data`

**功能**: 管理员设置云数据

**请求参数**:
- `action`: `"set_cloud_data"` (必需)
- `admin_password`: 管理员密码 (必需)
- `auth_code`: 激活码 (必需)
- `cloud_data`: 云数据内容 (必需，最大10000字符)

---

## 📱 客户端集成示例

### Python示例
```python
import requests
import json
from DES加密工具类 import DES加密工具类

class 网络验证客户端:
    def __init__(self, 服务器地址):
        self.服务器地址 = 服务器地址
        self.加密工具 = DES加密工具类()
        self.会话令牌 = None
        self.激活码 = None
    
    def 发送请求(self, 数据):
        """发送加密请求"""
        安全请求 = self.加密工具.创建安全请求(数据)
        
        response = requests.post(
            f"{self.服务器地址}/新版网络验证API.php",
            json=安全请求,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            响应数据 = response.json()
            return self.加密工具.解析安全响应(响应数据['encrypted_data'])
        else:
            raise Exception(f"HTTP错误: {response.status_code}")
    
    def 登录(self, 激活码, 机器码):
        """用户登录"""
        数据 = {
            'action': 'login',
            'auth_code': 激活码,
            'machine_code': 机器码
        }
        
        响应 = self.发送请求(数据)
        
        if 响应.get('status') == 'success':
            self.激活码 = 激活码
            self.会话令牌 = 响应.get('session_token')
            return 响应
        else:
            raise Exception(响应.get('message', '登录失败'))
    
    def 心跳检测(self, 机器码=None):
        """发送心跳检测"""
        if not self.激活码:
            raise Exception('请先登录')

        数据 = {
            'action': 'heartbeat',
            'auth_code': self.激活码
        }

        # 可选：添加机器码进行设备绑定验证
        if 机器码:
            数据['machine_code'] = 机器码

        return self.发送请求(数据)
```

### 使用示例

```python
# 初始化客户端
客户端 = 网络验证客户端("http://**************:8742")

# 登录
try:
    机器码 = "MACHINE_CODE_123456"
    响应 = 客户端.登录("STEAM123456789012345", 机器码)
    print(f"登录成功，剩余点数: {响应['user_info']['remaining_points']}")

    # 心跳检测（推荐带机器码验证）
    心跳响应 = 客户端.心跳检测(机器码)
    print(f"心跳状态: {心跳响应['status']}")
    print(f"剩余点数: {心跳响应.get('remaining_points', 'N/A')}")

    # 处理心跳失败的情况
    if 心跳响应['status'] != 'alive':
        print("心跳检测失败，程序将退出")
        exit(1)

except Exception as e:
    print(f"操作失败: {e}")
    # 心跳失败时应立即退出程序
    if "封禁" in str(e) or "无效" in str(e):
        print("检测到账号异常，程序退出")
        exit(1)
```

### C#示例
```csharp
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

public class NetworkAuthClient
{
    private readonly string serverUrl;
    private readonly HttpClient httpClient;
    private readonly DESEncryption encryption;

    public NetworkAuthClient(string serverUrl)
    {
        this.serverUrl = serverUrl;
        this.httpClient = new HttpClient();
        this.encryption = new DESEncryption();
    }

    public async Task<LoginResponse> LoginAsync(string authCode, string machineCode)
    {
        var requestData = new
        {
            action = "login",
            auth_code = authCode,
            machine_code = machineCode
        };

        var encryptedRequest = encryption.CreateSecureRequest(requestData);
        var json = JsonConvert.SerializeObject(encryptedRequest);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await httpClient.PostAsync($"{serverUrl}/新版网络验证API.php", content);
        var responseJson = await response.Content.ReadAsStringAsync();
        var responseData = JsonConvert.DeserializeObject<dynamic>(responseJson);

        var decryptedData = encryption.ParseSecureResponse(responseData.encrypted_data.ToString());
        return JsonConvert.DeserializeObject<LoginResponse>(decryptedData);
    }
}
```

### Java示例
```java
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import com.google.gson.Gson;

public class NetworkAuthClient {
    private final String serverUrl;
    private final HttpClient httpClient;
    private final DESEncryption encryption;

    public NetworkAuthClient(String serverUrl) {
        this.serverUrl = serverUrl;
        this.httpClient = HttpClient.newHttpClient();
        this.encryption = new DESEncryption();
    }

    public LoginResponse login(String authCode, String machineCode) throws Exception {
        LoginRequest requestData = new LoginRequest();
        requestData.action = "login";
        requestData.authCode = authCode;
        requestData.machineCode = machineCode;

        String encryptedRequest = encryption.createSecureRequest(requestData);

        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(serverUrl + "/新版网络验证API.php"))
            .header("Content-Type", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(encryptedRequest))
            .build();

        HttpResponse<String> response = httpClient.send(request,
            HttpResponse.BodyHandlers.ofString());

        Gson gson = new Gson();
        ResponseWrapper wrapper = gson.fromJson(response.body(), ResponseWrapper.class);
        String decryptedData = encryption.parseSecureResponse(wrapper.encryptedData);

        return gson.fromJson(decryptedData, LoginResponse.class);
    }
}
```

---

## 🚀 完整集成指南

### 1. 基础集成步骤

#### 第一步：获取硬件信息
```python
import platform
import uuid
import hashlib

def 获取机器码():
    """获取设备唯一标识"""
    # 获取MAC地址
    mac = uuid.getnode()
    # 获取CPU信息
    cpu_info = platform.processor()
    # 组合生成机器码
    machine_string = f"{mac}|{cpu_info}|{platform.machine()}"
    return hashlib.sha256(machine_string.encode()).hexdigest()[:32]
```

#### 第二步：实现加密通信
```python
from Crypto.Cipher import DES
from Crypto.Util.Padding import pad, unpad
import base64
import json
import time
import hashlib

class DES加密工具:
    def __init__(self, key='NETAUTH2025'):
        self.key = (key + '00000000')[:8].encode('utf-8')

    def encrypt(self, plaintext):
        cipher = DES.new(self.key, DES.MODE_ECB)
        if isinstance(plaintext, str):
            plaintext = plaintext.encode('utf-8')
        padded_data = pad(plaintext, DES.block_size)
        encrypted = cipher.encrypt(padded_data)
        return base64.b64encode(encrypted).decode('utf-8')

    def decrypt(self, ciphertext):
        cipher = DES.new(self.key, DES.MODE_ECB)
        encrypted_data = base64.b64decode(ciphertext)
        decrypted = cipher.decrypt(encrypted_data)
        return unpad(decrypted, DES.block_size).decode('utf-8')

    def create_secure_request(self, data):
        data['timestamp'] = int(time.time())
        data['sign'] = self.generate_signature(data)
        return {
            'encrypted_data': self.encrypt(json.dumps(data, ensure_ascii=False)),
            'timestamp': int(time.time())
        }

    def generate_signature(self, data):
        sorted_data = dict(sorted(data.items()))
        sign_string = ''
        for key, value in sorted_data.items():
            if key != 'sign':
                sign_string += f'{key}={value}&'
        sign_string = sign_string.rstrip('&') + self.key.decode('utf-8')
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
```

#### 第三步：实现网络验证类
```python
import requests
import threading
import time

class 网络验证系统:
    def __init__(self, server_url, auth_code):
        self.server_url = server_url
        self.auth_code = auth_code
        self.machine_code = 获取机器码()
        self.encryption = DES加密工具()
        self.session_token = None
        self.is_logged_in = False
        self.heartbeat_thread = None
        self.heartbeat_interval = 30  # 30秒心跳间隔

    def login(self):
        """登录验证"""
        data = {
            'action': 'login',
            'auth_code': self.auth_code,
            'machine_code': self.machine_code
        }

        response = self._send_request(data)

        if response.get('status') == 'success':
            self.session_token = response.get('session_token')
            self.is_logged_in = True
            self._start_heartbeat()
            return response
        else:
            raise Exception(response.get('message', '登录失败'))

    def _send_request(self, data):
        """发送加密请求"""
        secure_request = self.encryption.create_secure_request(data)

        response = requests.post(
            f"{self.server_url}/新版网络验证API.php",
            json=secure_request,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        if response.status_code == 200:
            response_data = response.json()
            decrypted = self.encryption.decrypt(response_data['encrypted_data'])
            return json.loads(decrypted)
        else:
            raise Exception(f"HTTP错误: {response.status_code}")

    def _start_heartbeat(self):
        """启动心跳检测"""
        def heartbeat_loop():
            while self.is_logged_in:
                try:
                    heartbeat_data = {
                        'action': 'heartbeat',
                        'auth_code': self.auth_code
                    }
                    response = self._send_request(heartbeat_data)

                    if response.get('status') != 'alive':
                        print("心跳检测失败，程序将退出")
                        self._force_exit()
                        break

                except Exception as e:
                    print(f"心跳检测异常: {e}")
                    self._force_exit()
                    break

                time.sleep(self.heartbeat_interval)

        self.heartbeat_thread = threading.Thread(target=heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()

    def _force_exit(self):
        """强制退出程序"""
        self.is_logged_in = False
        print("验证失败，程序即将退出...")
        import sys
        sys.exit(1)

    def verify_status(self):
        """验证状态"""
        if not self.is_logged_in:
            return False

        data = {
            'action': 'verify',
            'auth_code': self.auth_code,
            'session_token': self.session_token
        }

        try:
            response = self._send_request(data)
            return response.get('status') == 'valid'
        except:
            return False

    def get_cloud_data(self):
        """获取云数据"""
        data = {
            'action': 'get_cloud_data',
            'auth_code': self.auth_code
        }

        response = self._send_request(data)
        if response.get('status') == 'success':
            return response.get('cloud_data', '')
        else:
            raise Exception(response.get('message', '获取云数据失败'))

    def set_cloud_data(self, cloud_data):
        """设置云数据"""
        data = {
            'action': 'set_cloud_data',
            'auth_code': self.auth_code,
            'cloud_data': cloud_data
        }

        response = self._send_request(data)
        if response.get('status') != 'success':
            raise Exception(response.get('message', '设置云数据失败'))

    def logout(self):
        """登出"""
        self.is_logged_in = False
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=1)

        data = {
            'action': 'logout',
            'auth_code': self.auth_code
        }

        try:
            self._send_request(data)
        except:
            pass  # 登出时忽略网络错误
```

### 2. 使用示例

#### 基础使用
```python
# 初始化验证系统
auth_system = 网络验证系统(
    server_url="http://**************:8742",
    auth_code="STEAM123456789012345"
)

try:
    # 登录
    login_response = auth_system.login()
    print(f"登录成功！剩余点数: {login_response['user_info']['remaining_points']}")

    # 获取云数据
    cloud_data = auth_system.get_cloud_data()
    print(f"云数据: {cloud_data}")

    # 设置云数据
    auth_system.set_cloud_data("新的配置数据")

    # 验证状态
    if auth_system.verify_status():
        print("状态验证通过")

    # 程序主循环
    while auth_system.is_logged_in:
        # 你的程序逻辑
        time.sleep(1)

except Exception as e:
    print(f"验证失败: {e}")
finally:
    auth_system.logout()
```

#### 游戏集成示例
```python
class GameClient:
    def __init__(self):
        self.auth_system = None
        self.game_running = False

    def start_game(self, auth_code):
        """启动游戏"""
        try:
            # 初始化验证系统
            self.auth_system = 网络验证系统(
                server_url="http://**************:8742",
                auth_code=auth_code
            )

            # 登录验证
            login_response = self.auth_system.login()
            print(f"验证成功！欢迎使用，剩余点数: {login_response['user_info']['remaining_points']}")

            # 加载用户配置
            try:
                user_config = self.auth_system.get_cloud_data()
                if user_config:
                    self.load_user_config(user_config)
            except:
                pass  # 云数据可能为空

            # 启动游戏主循环
            self.game_running = True
            self.game_loop()

        except Exception as e:
            print(f"游戏启动失败: {e}")
            return False

    def game_loop(self):
        """游戏主循环"""
        while self.game_running and self.auth_system.is_logged_in:
            # 游戏逻辑
            self.update_game()

            # 定期验证状态（可选）
            if not self.auth_system.verify_status():
                print("验证状态失效，游戏将退出")
                break

            time.sleep(0.016)  # 60FPS

    def save_and_exit(self):
        """保存并退出"""
        try:
            # 保存用户配置到云端
            user_config = self.get_user_config()
            self.auth_system.set_cloud_data(user_config)
        except:
            pass

        # 登出
        if self.auth_system:
            self.auth_system.logout()

        self.game_running = False
```

---

## ⚠️ 错误代码说明

| 错误信息 | 说明 | 解决方案 |
|---------|------|----------|
| 激活码无效或已过期 | 激活码不存在或已过期 | 检查激活码是否正确 |
| 点数不足，无法登录 | 激活码点数为0 | 联系管理员充值 |
| 此激活码已绑定其他设备 | 设备绑定冲突 | 先解绑再登录 |
| IP地址已被封禁 | IP被管理员封禁 | 联系管理员解封 |
| 设备已被封禁 | 机器码被封禁 | 联系管理员解封 |
| 会话令牌无效 | 登录状态失效 | 重新登录 |
| 今日解绑次数已用完 | 超过每日解绑限制 | 明天再试或联系管理员 |
| 激活码已被封禁 | 心跳检测时发现激活码被封禁 | 立即退出程序，联系管理员 |
| 点数不足，心跳检测失败 | 心跳时点数不足 | 立即退出程序，联系管理员充值 |
| 设备不匹配，心跳检测失败 | 心跳时设备绑定不匹配 | 立即退出程序，检查设备信息 |

---

## 🛡️ 安全建议

### 1. 通信安全
- **强制加密**: 所有API调用必须使用加密接口
- **签名验证**: 每个请求都包含签名防止篡改
- **时间戳检查**: 防止重放攻击，请求有效期5分钟
- **HTTPS传输**: 生产环境建议使用HTTPS

### 2. 客户端安全
- **定时心跳**: 每30-60秒发送心跳检测，实时验证账号状态
  - 包含激活码封禁检查
  - 包含IP封禁检查
  - 包含点数余额检查
  - 包含设备绑定验证
- **状态验证**: 关键操作前验证登录状态
- **强制退出**: 心跳失败或检测到封禁时立即退出程序
- **异常处理**: 妥善处理心跳异常，避免程序崩溃
- **防调试**: 可添加反调试和完整性检查

### 3. 错误处理
- **网络异常**: 妥善处理超时和连接错误
- **服务器错误**: 根据错误码提供友好提示
- **重试机制**: 网络不稳定时自动重试
- **日志记录**: 记录关键操作和错误信息

### 4. 性能优化
- **连接复用**: 使用HTTP连接池
- **异步处理**: 心跳检测使用后台线程
- **缓存机制**: 缓存验证结果减少请求
- **超时设置**: 合理设置请求超时时间

---

## 🔧 最佳实践

### 1. 初始化检查
```python
def initialize_auth():
    """初始化验证系统"""
    # 检查网络连接
    if not check_network_connection():
        raise Exception("网络连接失败")

    # 检查服务器状态
    if not check_server_status():
        raise Exception("服务器不可用")

    # 获取硬件信息
    machine_code = get_machine_code()
    if not machine_code:
        raise Exception("无法获取设备信息")

    return machine_code
```

### 2. 优雅退出
```python
def graceful_shutdown():
    """优雅退出"""
    try:
        # 保存用户数据
        save_user_data()

        # 上传云数据
        if auth_system.is_logged_in:
            auth_system.set_cloud_data(get_user_config())

        # 登出
        auth_system.logout()

    except Exception as e:
        print(f"退出时发生错误: {e}")
    finally:
        # 清理资源
        cleanup_resources()
```

### 3. 异常恢复
```python
def handle_auth_failure():
    """处理验证失败"""
    try:
        # 尝试重新登录
        auth_system.login()
        return True
    except Exception as e:
        # 记录错误
        log_error(f"重新登录失败: {e}")

        # 显示错误信息
        show_error_dialog("验证失败，请检查激活码或网络连接")

        # 退出程序
        sys.exit(1)
```

### 4. 配置管理
```python
class ConfigManager:
    def __init__(self):
        self.config = {
            'server_url': 'http://**************:8742',
            'heartbeat_interval': 30,
            'request_timeout': 30,
            'max_retries': 3
        }

    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r') as f:
                user_config = json.load(f)
                self.config.update(user_config)
        except:
            pass  # 使用默认配置

    def save_config(self):
        """保存配置文件"""
        with open('config.json', 'w') as f:
            json.dump(self.config, f, indent=2)
```

---

## ❓ 常见问题解答

### Q1: 如何处理网络不稳定的情况？
**A**: 实现重试机制和超时处理：
```python
def send_request_with_retry(data, max_retries=3):
    for attempt in range(max_retries):
        try:
            return auth_system._send_request(data)
        except requests.exceptions.Timeout:
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
                continue
            raise
        except requests.exceptions.ConnectionError:
            if attempt < max_retries - 1:
                time.sleep(1)
                continue
            raise
```

### Q2: 如何实现离线模式？
**A**: 可以缓存验证结果，但需要定期在线验证：
```python
class OfflineAuth:
    def __init__(self):
        self.last_online_check = 0
        self.offline_grace_period = 3600  # 1小时离线宽限期

    def can_work_offline(self):
        current_time = time.time()
        return (current_time - self.last_online_check) < self.offline_grace_period
```

### Q3: 如何防止激活码被盗用？
**A**: 使用设备绑定和IP限制：
- 激活码首次使用时绑定设备
- 记录登录IP地址
- 异常登录时发送警告
- 支持用户主动解绑设备

### Q4: 如何处理时区问题？
**A**: 统一使用UTC时间：
```python
import datetime

def get_utc_timestamp():
    return int(datetime.datetime.utcnow().timestamp())

def format_server_time(server_time_str):
    # 服务器时间转换为本地时间显示
    server_time = datetime.datetime.strptime(server_time_str, '%Y-%m-%d %H:%M:%S')
    local_time = server_time.replace(tzinfo=datetime.timezone.utc).astimezone()
    return local_time.strftime('%Y-%m-%d %H:%M:%S')
```

### Q5: 如何实现多语言支持？
**A**: 创建错误消息映射表：
```python
ERROR_MESSAGES = {
    'zh': {
        'invalid_auth_code': '激活码无效或已过期',
        'insufficient_points': '点数不足，无法登录',
        'device_banned': '设备已被封禁'
    },
    'en': {
        'invalid_auth_code': 'Invalid or expired activation code',
        'insufficient_points': 'Insufficient points for login',
        'device_banned': 'Device has been banned'
    }
}

def get_error_message(error_key, language='zh'):
    return ERROR_MESSAGES.get(language, ERROR_MESSAGES['zh']).get(error_key, error_key)
```

---

## 📊 性能监控

### 1. 请求统计
```python
class RequestStats:
    def __init__(self):
        self.total_requests = 0
        self.failed_requests = 0
        self.average_response_time = 0

    def record_request(self, success, response_time):
        self.total_requests += 1
        if not success:
            self.failed_requests += 1

        # 计算平均响应时间
        self.average_response_time = (
            (self.average_response_time * (self.total_requests - 1) + response_time)
            / self.total_requests
        )

    def get_success_rate(self):
        if self.total_requests == 0:
            return 0
        return (self.total_requests - self.failed_requests) / self.total_requests * 100
```

### 2. 健康检查
```python
def health_check():
    """系统健康检查"""
    checks = {
        'network': check_network_connection(),
        'server': check_server_status(),
        'auth': auth_system.is_logged_in if auth_system else False,
        'heartbeat': check_heartbeat_status()
    }

    all_healthy = all(checks.values())
    return {
        'healthy': all_healthy,
        'checks': checks,
        'timestamp': time.time()
    }
```

---

## 📞 技术支持

### 联系方式
- **系统管理员**: 查看服务器日志获取详细错误信息
- **开发文档**: 本文档包含完整的API说明和集成示例
- **错误排查**: 参考错误代码说明和常见问题解答

### 调试建议
1. **启用详细日志**: 记录所有API请求和响应
2. **网络抓包**: 使用Wireshark等工具分析网络通信
3. **服务器日志**: 查看服务器端的错误日志
4. **逐步调试**: 从简单的心跳检测开始测试

### 更新说明
- 本文档基于网络验证系统v2.0编写
- 支持激活码系统、点数计费、云数据等功能
- 如有API变更，请及时更新客户端代码

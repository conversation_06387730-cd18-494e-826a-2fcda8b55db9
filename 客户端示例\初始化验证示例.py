#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络验证系统客户端 - 初始化验证示例
演示如何在登录前进行软件标识和版本号验证
"""

import sys
import requests
import json
from DES加密工具类 import DES加密工具类
from 硬件信息获取类 import 硬件信息获取类

class 初始化验证客户端:
    def __init__(self):
        # 服务器配置
        self.服务器地址 = "http://222.186.21.133:8742"
        self.API地址 = f"{self.服务器地址}/新版网络验证API.php"
        
        # 客户端配置 - 这些参数需要与服务端配置一致
        self.软件标识 = "123456789012345678"  # 18位软件标识，必须与服务端一致
        self.软件版本号 = "1.0.0"  # 版本号，必须与服务端一致
        
        # 工具类
        self.加密工具 = DES加密工具类()
        self.硬件工具 = 硬件信息获取类()
        
        print("=" * 60)
        print("网络验证系统客户端 - 初始化验证示例")
        print("=" * 60)
        print(f"服务器地址: {self.服务器地址}")
        print(f"软件标识: {self.软件标识}")
        print(f"软件版本: {self.软件版本号}")
        print("=" * 60)
    
    def 发送请求(self, 数据):
        """发送加密请求到服务器"""
        try:
            print(f"发送请求: {数据.get('action', 'unknown')}")
            
            # 创建加密请求
            安全请求 = self.加密工具.创建安全请求(数据)
            
            # 发送请求
            response = requests.post(
                self.API地址,
                json=安全请求,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code != 200:
                raise Exception(f"HTTP错误: {response.status_code}")
            
            响应数据 = response.json()
            
            # 解析加密响应
            if 'encrypted_data' in 响应数据:
                解密响应 = self.加密工具.解析安全响应(响应数据['encrypted_data'])
                return 解密响应
            else:
                return 响应数据
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"响应格式错误: {str(e)}")
        except Exception as e:
            raise Exception(f"请求失败: {str(e)}")
    
    def 执行初始化验证(self):
        """执行初始化验证"""
        try:
            print("\n开始执行初始化验证...")
            
            # 准备初始化数据
            初始化数据 = {
                'action': 'init',
                'software_id': self.软件标识,
                'software_version': self.软件版本号
            }
            
            # 发送初始化请求
            响应 = self.发送请求(初始化数据)
            
            if 响应.get('status') == 'success':
                print("✓ 初始化验证成功！")
                print(f"  服务器软件标识: {响应['server_info']['software_id']}")
                print(f"  服务器软件版本: {响应['server_info']['software_version']}")
                print(f"  服务器时间: {响应['server_info']['server_time']}")
                return True
            else:
                print(f"✗ 初始化验证失败: {响应.get('message', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"✗ 初始化验证异常: {str(e)}")
            return False
    
    def 执行登录验证(self, 激活码):
        """执行登录验证（在初始化成功后）"""
        try:
            print(f"\n开始登录验证，激活码: {激活码}")
            
            # 获取机器码
            机器码 = self.硬件工具.获取机器码()
            CPU序列号 = self.硬件工具.获取CPU序列号()
            
            print(f"机器码: {机器码}")
            print(f"CPU序列号: {CPU序列号}")
            
            # 准备登录数据
            登录数据 = {
                'action': 'login',
                'auth_code': 激活码,
                'machine_code': 机器码,
                'cpu_serial': CPU序列号
            }
            
            # 发送登录请求
            响应 = self.发送请求(登录数据)
            
            if 响应.get('status') == 'success':
                print("✓ 登录验证成功！")
                print(f"  剩余点数: {响应['user_info']['remaining_points']}")
                print(f"  登录时间: {响应['user_info']['login_time']}")
                print(f"  总登录次数: {响应['user_info']['total_logins']}")
                
                if 'points_info' in 响应:
                    点数信息 = 响应['points_info']
                    print(f"  扣除前点数: {点数信息['before_points']}")
                    print(f"  扣除点数: {点数信息['deducted_points']}")
                    print(f"  扣除后点数: {点数信息['after_points']}")
                
                return True, 响应.get('session_token')
            else:
                print(f"✗ 登录验证失败: {响应.get('message', '未知错误')}")
                return False, None
                
        except Exception as e:
            print(f"✗ 登录验证异常: {str(e)}")
            return False, None
    
    def 运行示例(self):
        """运行完整的验证示例"""
        try:
            # 步骤1: 执行初始化验证
            if not self.执行初始化验证():
                print("\n初始化验证失败，程序退出。")
                print("请检查：")
                print("1. 软件标识是否与服务端一致（18位）")
                print("2. 软件版本号是否与服务端一致")
                print("3. 网络连接是否正常")
                return False
            
            # 步骤2: 获取激活码
            print("\n" + "=" * 40)
            激活码 = input("请输入激活码: ").strip()
            
            if not 激活码:
                print("激活码不能为空")
                return False
            
            # 步骤3: 执行登录验证
            登录成功, 会话令牌 = self.执行登录验证(激活码)
            
            if 登录成功:
                print("\n" + "=" * 40)
                print("验证流程完成！客户端可以正常使用。")
                print(f"会话令牌: {会话令牌[:20]}...")
                return True
            else:
                print("\n登录验证失败，程序退出。")
                return False
                
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            return False
        except Exception as e:
            print(f"\n运行异常: {str(e)}")
            return False

def main():
    """主函数"""
    try:
        客户端 = 初始化验证客户端()
        
        # 运行验证示例
        成功 = 客户端.运行示例()
        
        if 成功:
            print("\n程序执行成功！")
            sys.exit(0)
        else:
            print("\n程序执行失败！")
            sys.exit(1)
            
    except Exception as e:
        print(f"程序异常: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()

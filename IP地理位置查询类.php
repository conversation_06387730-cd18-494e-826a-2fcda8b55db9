<?php
/**
 * IP地理位置查询类
 * 使用ip9.com.cn API查询IP地理位置信息
 */

class IP地理位置查询类 {
    private $api_url = 'https://ip9.com.cn/get';
    private $timeout = 5; // 5秒超时
    
    /**
     * 查询IP地理位置信息
     * @param string $ip IP地址
     * @return array 地理位置信息
     */
    public function 查询IP位置($ip) {
        try {
            // 验证IP地址格式
            if (!filter_var($ip, FILTER_VALIDATE_IP)) {
                throw new Exception("无效的IP地址: {$ip}");
            }
            
            // 内网IP直接返回默认值
            if ($this->是内网IP($ip)) {
                return $this->获取默认位置信息($ip, '内网IP');
            }
            
            // 构建请求URL
            $请求URL = $this->api_url . '?ip=' . urlencode($ip);
            
            // 发送HTTP请求
            $响应 = $this->发送HTTP请求($请求URL);
            
            if (empty($响应)) {
                throw new Exception("API响应为空");
            }
            
            // 解析JSON响应
            $数据 = json_decode($响应, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("JSON解析失败: " . json_last_error_msg());
            }
            
            // 检查API返回状态
            if (!isset($数据['ret']) || $数据['ret'] != 200) {
                throw new Exception("API返回错误: " . ($数据['msg'] ?? '未知错误'));
            }
            
            // 提取地理位置信息
            $位置数据 = $数据['data'] ?? [];
            
            return [
                'success' => true,
                'ip' => $ip,
                'country' => $位置数据['country'] ?? '未知',
                'province' => $位置数据['prov'] ?? '未知',
                'city' => $位置数据['city'] ?? '未知',
                'area' => $位置数据['area'] ?? '未知',
                'isp' => $位置数据['isp'] ?? '未知',
                'longitude' => $位置数据['lng'] ?? '',
                'latitude' => $位置数据['lat'] ?? '',
                'country_code' => $位置数据['country_code'] ?? '',
                'raw_data' => json_encode($位置数据, JSON_UNESCAPED_UNICODE),
                'query_time' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            // 查询失败时返回默认信息
            return $this->获取默认位置信息($ip, $e->getMessage());
        }
    }
    
    /**
     * 发送HTTP请求
     * @param string $url 请求URL
     * @return string 响应内容
     */
    private function 发送HTTP请求($url) {
        // 优先使用cURL
        if (function_exists('curl_init')) {
            return $this->使用cURL请求($url);
        }
        
        // 备用方案：使用file_get_contents
        if (ini_get('allow_url_fopen')) {
            return $this->使用文件流请求($url);
        }
        
        throw new Exception("无可用的HTTP请求方法");
    }
    
    /**
     * 使用cURL发送请求
     * @param string $url 请求URL
     * @return string 响应内容
     */
    private function 使用cURL请求($url) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_CONNECTTIMEOUT => 3,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => 'NetworkAuth-Server/1.0',
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'Accept-Language: zh-CN,zh;q=0.9'
            ]
        ]);
        
        $响应 = curl_exec($ch);
        $错误 = curl_error($ch);
        $状态码 = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        curl_close($ch);
        
        if ($错误) {
            throw new Exception("cURL错误: {$错误}");
        }
        
        if ($状态码 !== 200) {
            throw new Exception("HTTP错误: {$状态码}");
        }
        
        return $响应;
    }
    
    /**
     * 使用文件流发送请求
     * @param string $url 请求URL
     * @return string 响应内容
     */
    private function 使用文件流请求($url) {
        $上下文 = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => $this->timeout,
                'user_agent' => 'NetworkAuth-Server/1.0',
                'header' => [
                    'Accept: application/json',
                    'Accept-Language: zh-CN,zh;q=0.9'
                ]
            ]
        ]);
        
        $响应 = file_get_contents($url, false, $上下文);
        
        if ($响应 === false) {
            throw new Exception("文件流请求失败");
        }
        
        return $响应;
    }
    
    /**
     * 检查是否为内网IP
     * @param string $ip IP地址
     * @return bool 是否为内网IP
     */
    private function 是内网IP($ip) {
        return !filter_var(
            $ip, 
            FILTER_VALIDATE_IP, 
            FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE
        );
    }
    
    /**
     * 获取默认位置信息
     * @param string $ip IP地址
     * @param string $原因 失败原因
     * @return array 默认位置信息
     */
    private function 获取默认位置信息($ip, $原因 = '') {
        return [
            'success' => false,
            'ip' => $ip,
            'country' => '未知',
            'province' => '未知',
            'city' => '未知',
            'area' => '未知',
            'isp' => '未知',
            'longitude' => '',
            'latitude' => '',
            'country_code' => '',
            'raw_data' => json_encode(['error' => $原因], JSON_UNESCAPED_UNICODE),
            'query_time' => date('Y-m-d H:i:s'),
            'error' => $原因
        ];
    }
    
    /**
     * 批量查询IP位置（异步处理）
     * @param array $ip列表 IP地址数组
     * @return array 查询结果数组
     */
    public function 批量查询IP位置($ip列表) {
        $结果 = [];
        
        foreach ($ip列表 as $ip) {
            $结果[$ip] = $this->查询IP位置($ip);
            
            // 避免请求过于频繁
            if (count($ip列表) > 1) {
                usleep(200000); // 延迟200毫秒
            }
        }
        
        return $结果;
    }
    
    /**
     * 格式化位置信息为字符串
     * @param array $位置信息 位置信息数组
     * @return string 格式化的位置字符串
     */
    public function 格式化位置信息($位置信息) {
        if (!$位置信息['success']) {
            return '位置未知';
        }
        
        $位置部分 = [];
        
        if ($位置信息['country'] && $位置信息['country'] !== '未知') {
            $位置部分[] = $位置信息['country'];
        }
        
        if ($位置信息['province'] && $位置信息['province'] !== '未知') {
            $位置部分[] = $位置信息['province'];
        }
        
        if ($位置信息['city'] && $位置信息['city'] !== '未知') {
            $位置部分[] = $位置信息['city'];
        }
        
        if ($位置信息['area'] && $位置信息['area'] !== '未知') {
            $位置部分[] = $位置信息['area'];
        }
        
        $位置字符串 = implode(' ', $位置部分);
        
        if ($位置信息['isp'] && $位置信息['isp'] !== '未知') {
            $位置字符串 .= ' (' . $位置信息['isp'] . ')';
        }
        
        return $位置字符串 ?: '位置未知';
    }
    
    /**
     * 测试IP查询功能
     * @return array 测试结果
     */
    public function 测试查询功能() {
        $测试IP列表 = [
            '*******',      // Google DNS
            '***************', // 114 DNS
            '***********',  // 内网IP
            '127.0.0.1'     // 本地IP
        ];
        
        $测试结果 = [];
        
        foreach ($测试IP列表 as $ip) {
            $开始时间 = microtime(true);
            $结果 = $this->查询IP位置($ip);
            $耗时 = round((microtime(true) - $开始时间) * 1000, 2);
            
            $测试结果[] = [
                'ip' => $ip,
                'success' => $结果['success'],
                'location' => $this->格式化位置信息($结果),
                'time_ms' => $耗时,
                'details' => $结果
            ];
        }
        
        return $测试结果;
    }
}
?>

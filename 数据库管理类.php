<?php
/**
 * 数据库管理类
 * 负责MySQL数据库的连接和基础操作
 */

class 数据库管理类 {
    private $pdo;

    // MySQL数据库配置
    private $数据库主机 = 'localhost';
    private $数据库端口 = 3306;
    private $数据库名称 = '218_93_208_123_8';
    private $用户名 = '218_93_208_123_8';
    private $密码 = 'd13f1T1SeYwf5XnF';
    private $字符集 = 'utf8mb4';

    public function __construct() {
        $this->连接数据库();
        $this->初始化数据库();
    }

    /**
     * 连接MySQL数据库
     */
    private function 连接数据库() {
        try {
            $dsn = "mysql:host={$this->数据库主机};port={$this->数据库端口};dbname={$this->数据库名称};charset={$this->字符集}";
            $this->pdo = new PDO($dsn, $this->用户名, $this->密码, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->字符集}",
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 初始化数据库表结构
     */
    private function 初始化数据库() {
        $sql文件 = __DIR__ . '/数据库初始化.sql';
        if (file_exists($sql文件)) {
            $sql内容 = file_get_contents($sql文件);
            $this->pdo->exec($sql内容);
        }
    }
    
    /**
     * 获取PDO连接对象
     */
    public function 获取连接() {
        return $this->pdo;
    }
    
    /**
     * 执行查询
     */
    public function 查询($sql, $参数 = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($参数);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("查询执行失败: " . $e->getMessage());
        }
    }
    
    /**
     * 执行单条查询
     */
    public function 查询单条($sql, $参数 = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($参数);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("查询执行失败: " . $e->getMessage());
        }
    }
    
    /**
     * 执行插入/更新/删除操作
     */
    public function 执行($sql, $参数 = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute($参数);
            return [
                'success' => $result,
                'affected_rows' => $stmt->rowCount(),
                'last_insert_id' => $this->pdo->lastInsertId()
            ];
        } catch (PDOException $e) {
            throw new Exception("执行失败: " . $e->getMessage());
        }
    }
    
    /**
     * 开始事务
     */
    public function 开始事务() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function 提交事务() {
        return $this->pdo->commit();
    }
    
    /**
     * 回滚事务
     */
    public function 回滚事务() {
        return $this->pdo->rollBack();
    }
    
    /**
     * 验证激活码是否存在且有效
     */
    public function 验证激活码($激活码) {
        $sql = "SELECT * FROM `激活码表` WHERE `激活码` = ? AND `激活码状态` != 2";
        return $this->查询单条($sql, [$激活码]);
    }

    /**
     * 检查激活码是否被封禁
     */
    public function 检查激活码封禁($激活码) {
        // 检查激活码本身是否被封禁
        $激活码封禁 = $this->查询单条("SELECT * FROM `激活码表` WHERE `激活码` = ? AND `激活码状态` = 2", [$激活码]);
        if ($激活码封禁) {
            return ['banned' => true, 'reason' => $激活码封禁['封禁原因'], 'type' => '激活码封禁'];
        }

        // 检查封禁列表
        $封禁记录 = $this->查询单条("SELECT * FROM `封禁列表表` WHERE `封禁类型` = '激活码' AND `封禁值` = ? AND `状态` = 1 AND (`解封时间` IS NULL OR `解封时间` > NOW())", [$激活码]);
        if ($封禁记录) {
            return ['banned' => true, 'reason' => $封禁记录['封禁原因'], 'type' => '列表封禁'];
        }

        return ['banned' => false];
    }

    /**
     * 检查IP是否被封禁
     */
    public function 检查IP封禁($IP地址) {
        $封禁记录 = $this->查询单条("SELECT * FROM `封禁列表表` WHERE `封禁类型` = 'IP' AND `封禁值` = ? AND `状态` = 1 AND (`解封时间` IS NULL OR `解封时间` > NOW())", [$IP地址]);
        if ($封禁记录) {
            return ['banned' => true, 'reason' => $封禁记录['封禁原因'], 'type' => 'IP封禁'];
        }
        return ['banned' => false];
    }

    /**
     * 检查机器码是否被封禁
     */
    public function 检查机器码封禁($机器码) {
        $封禁记录 = $this->查询单条("SELECT * FROM `封禁列表表` WHERE `封禁类型` = '机器码' AND `封禁值` = ? AND `状态` = 1 AND (`解封时间` IS NULL OR `解封时间` > NOW())", [$机器码]);
        if ($封禁记录) {
            return ['banned' => true, 'reason' => $封禁记录['封禁原因'], 'type' => '机器码封禁'];
        }
        return ['banned' => false];
    }

    /**
     * 更新激活码绑定机器码和使用状态
     */
    public function 更新激活码绑定($激活码, $机器码) {
        // 检查是否首次使用
        $激活码信息 = $this->查询单条("SELECT `激活码状态`, `首次使用时间` FROM `激活码表` WHERE `激活码` = ?", [$激活码]);

        $首次使用时间 = null;
        $新状态 = 1; // 已使用

        if ($激活码信息 && $激活码信息['激活码状态'] == 0) {
            // 首次使用
            $首次使用时间 = date('Y-m-d H:i:s');
        }

        $sql = "UPDATE `激活码表` SET `绑定机器码` = ?, `激活码状态` = ?, `最后登录时间` = NOW(), `总登录次数` = `总登录次数` + 1";
        $参数 = [$机器码, $新状态];

        if ($首次使用时间) {
            $sql .= ", `首次使用时间` = ?";
            $参数[] = $首次使用时间;
        }

        $sql .= " WHERE `激活码` = ?";
        $参数[] = $激活码;

        return $this->执行($sql, $参数);
    }

    /**
     * 扣除激活码点数
     */
    public function 扣除激活码点数($激活码, $扣除点数, $原因 = '登录扣除', $操作员 = 'system', $登录IP = null) {
        // 开始事务
        $this->pdo->beginTransaction();

        try {
            // 获取当前点数
            $当前信息 = $this->查询单条("SELECT `剩余点数`, `每次扣除点数` FROM `激活码表` WHERE `激活码` = ?", [$激活码]);
            if (!$当前信息) {
                throw new Exception("激活码不存在");
            }

            $当前点数 = $当前信息['剩余点数'];
            $新点数 = $当前点数 - $扣除点数;

            if ($新点数 < 0) {
                throw new Exception("点数不足");
            }

            // 更新激活码点数
            $this->执行("UPDATE `激活码表` SET `剩余点数` = ? WHERE `激活码` = ?", [$新点数, $激活码]);

            // 记录点数变动
            $this->执行("INSERT INTO `点数变动记录表` (`激活码`, `变动类型`, `变动前点数`, `变动点数`, `变动后点数`, `变动原因`, `操作员`, `登录IP`) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                [$激活码, $原因, $当前点数, -$扣除点数, $新点数, $原因, $操作员, $登录IP]);

            $this->pdo->commit();

            return [
                'success' => true,
                'before_points' => $当前点数,
                'deducted_points' => $扣除点数,
                'after_points' => $新点数
            ];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 增加激活码点数
     */
    public function 增加激活码点数($激活码, $增加点数, $原因 = '手动充值', $操作员 = 'admin', $登录IP = null) {
        // 开始事务
        $this->pdo->beginTransaction();

        try {
            // 获取当前点数
            $当前信息 = $this->查询单条("SELECT `剩余点数` FROM `激活码表` WHERE `激活码` = ?", [$激活码]);
            if (!$当前信息) {
                throw new Exception("激活码不存在");
            }

            $当前点数 = $当前信息['剩余点数'];
            $新点数 = $当前点数 + $增加点数;

            // 更新激活码点数
            $this->执行("UPDATE `激活码表` SET `剩余点数` = ? WHERE `激活码` = ?", [$新点数, $激活码]);

            // 记录点数变动
            $this->执行("INSERT INTO `点数变动记录表` (`激活码`, `变动类型`, `变动前点数`, `变动点数`, `变动后点数`, `变动原因`, `操作员`, `登录IP`) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                [$激活码, $原因, $当前点数, $增加点数, $新点数, $原因, $操作员, $登录IP]);

            $this->pdo->commit();

            return [
                'success' => true,
                'before_points' => $当前点数,
                'added_points' => $增加点数,
                'after_points' => $新点数
            ];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 检查解绑限制
     */
    public function 检查解绑限制($激活码) {
        $激活码信息 = $this->查询单条("SELECT `每日解绑次数`, `解绑重置时间`, `最大解绑次数` FROM `激活码表` WHERE `激活码` = ?", [$激活码]);

        if (!$激活码信息) {
            return ['allowed' => false, 'reason' => '激活码不存在'];
        }

        $今日 = date('Y-m-d');
        $解绑重置时间 = $激活码信息['解绑重置时间'];
        $每日解绑次数 = $激活码信息['每日解绑次数'];
        $最大解绑次数 = $激活码信息['最大解绑次数'];

        // 检查是否需要重置计数
        if ($解绑重置时间 !== $今日) {
            // 重置解绑计数
            $this->执行("UPDATE `激活码表` SET `每日解绑次数` = 0, `解绑重置时间` = ? WHERE `激活码` = ?", [$今日, $激活码]);
            $每日解绑次数 = 0;
        }

        if ($每日解绑次数 >= $最大解绑次数) {
            return [
                'allowed' => false,
                'reason' => "今日解绑次数已达上限({$最大解绑次数}次)",
                'used_count' => $每日解绑次数,
                'max_count' => $最大解绑次数
            ];
        }

        return [
            'allowed' => true,
            'used_count' => $每日解绑次数,
            'max_count' => $最大解绑次数,
            'remaining' => $最大解绑次数 - $每日解绑次数
        ];
    }

    /**
     * 执行设备解绑
     */
    public function 执行设备解绑($激活码) {
        // 检查解绑限制
        $解绑检查 = $this->检查解绑限制($激活码);
        if (!$解绑检查['allowed']) {
            return ['success' => false, 'message' => $解绑检查['reason']];
        }

        // 开始事务
        $this->pdo->beginTransaction();

        try {
            // 清除绑定机器码
            $this->执行("UPDATE `激活码表` SET `绑定机器码` = NULL WHERE `激活码` = ?", [$激活码]);

            // 增加解绑次数
            $this->执行("UPDATE `激活码表` SET `每日解绑次数` = `每日解绑次数` + 1, `解绑重置时间` = ? WHERE `激活码` = ?", [date('Y-m-d'), $激活码]);

            $this->pdo->commit();

            return [
                'success' => true,
                'message' => '设备解绑成功',
                'remaining_unbinds' => $解绑检查['remaining'] - 1
            ];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            return ['success' => false, 'message' => '解绑失败: ' . $e->getMessage()];
        }
    }

    /**
     * 设置激活码云数据（纯文本格式）
     */
    public function 设置云数据($激活码, $云数据) {
        try {
            // 首先检查激活码是否存在
            $检查结果 = $this->查询单条("SELECT `激活码` FROM `激活码表` WHERE `激活码` = ?", [$激活码]);
            if (!$检查结果) {
                return ['success' => false, 'message' => '激活码不存在'];
            }

            // 云数据直接存储为纯文本，不进行JSON编码
            $sql = "UPDATE `激活码表` SET `云数据` = ? WHERE `激活码` = ?";
            $结果 = $this->执行($sql, [$云数据, $激活码]);

            if ($结果['success'] && $结果['affected_rows'] > 0) {
                return ['success' => true, 'message' => '云数据设置成功'];
            } else {
                return ['success' => false, 'message' => '云数据设置失败，没有行被更新'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => '云数据设置异常: ' . $e->getMessage()];
        }
    }

    /**
     * 获取激活码云数据（纯文本格式）
     */
    public function 获取云数据($激活码) {
        $结果 = $this->查询单条("SELECT `云数据` FROM `激活码表` WHERE `激活码` = ?", [$激活码]);
        if ($结果) {
            // 直接返回纯文本云数据，如果为空则返回空字符串
            return $结果['云数据'] ?? '';
        }
        return '';
    }

    /**
     * 记录登录日志（包含IP地理位置信息和点数信息）
     */
    public function 记录登录日志($激活码, $登录IP, $机器码, $CPU序列号, $登录状态, $失败原因 = null, $用户代理 = null, $IP位置信息 = null, $点数信息 = null) {
        $sql = "INSERT INTO `登录记录表` (`激活码`, `验证码`, `登录IP`, `机器码`, `CPU序列号`, `登录状态`, `失败原因`, `用户代理`, `扣除点数`, `登录前点数`, `登录后点数`, `ip_country`, `ip_province`, `ip_city`, `ip_area`, `ip_isp`, `ip_location_json`)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        // 提取IP位置信息
        $国家 = $IP位置信息['country'] ?? null;
        $省份 = $IP位置信息['province'] ?? null;
        $城市 = $IP位置信息['city'] ?? null;
        $区县 = $IP位置信息['area'] ?? null;
        $运营商 = $IP位置信息['isp'] ?? null;
        $位置JSON = $IP位置信息 ? json_encode($IP位置信息, JSON_UNESCAPED_UNICODE) : null;

        // 提取点数信息
        $扣除点数 = $点数信息['deducted_points'] ?? 0;
        $登录前点数 = $点数信息['before_points'] ?? null;
        $登录后点数 = $点数信息['after_points'] ?? null;

        // 验证码字段（兼容旧版）- 使用激活码作为验证码
        $验证码 = $激活码;

        return $this->执行($sql, [$激活码, $验证码, $登录IP, $机器码, $CPU序列号, $登录状态, $失败原因, $用户代理, $扣除点数, $登录前点数, $登录后点数, $国家, $省份, $城市, $区县, $运营商, $位置JSON]);
    }

    /**
     * 获取系统配置
     */
    public function 获取配置($配置键) {
        $sql = "SELECT `配置值` FROM `系统配置表` WHERE `配置键` = ?";
        $result = $this->查询单条($sql, [$配置键]);
        return $result ? $result['配置值'] : null;
    }

    /**
     * 获取IP地理位置统计
     */
    public function 获取IP地理位置统计($限制 = 100) {
        // 按国家统计
        $国家统计 = $this->查询("
            SELECT `ip_country`, COUNT(*) as count
            FROM `登录记录表`
            WHERE `ip_country` IS NOT NULL AND `ip_country` != '未知'
            GROUP BY `ip_country`
            ORDER BY count DESC
            LIMIT ?
        ", [$限制]);

        // 按省份统计
        $省份统计 = $this->查询("
            SELECT `ip_province`, COUNT(*) as count
            FROM `登录记录表`
            WHERE `ip_province` IS NOT NULL AND `ip_province` != '未知'
            GROUP BY `ip_province`
            ORDER BY count DESC
            LIMIT ?
        ", [$限制]);

        // 按城市统计
        $城市统计 = $this->查询("
            SELECT `ip_city`, COUNT(*) as count
            FROM `登录记录表`
            WHERE `ip_city` IS NOT NULL AND `ip_city` != '未知'
            GROUP BY `ip_city`
            ORDER BY count DESC
            LIMIT ?
        ", [$限制]);

        // 按运营商统计
        $运营商统计 = $this->查询("
            SELECT `ip_isp`, COUNT(*) as count
            FROM `登录记录表`
            WHERE `ip_isp` IS NOT NULL AND `ip_isp` != '未知'
            GROUP BY `ip_isp`
            ORDER BY count DESC
            LIMIT ?
        ", [$限制]);

        return [
            'country_stats' => $国家统计,
            'province_stats' => $省份统计,
            'city_stats' => $城市统计,
            'isp_stats' => $运营商统计
        ];
    }

    /**
     * 添加封禁记录
     */
    public function 添加封禁($封禁类型, $封禁值, $封禁原因, $解封时间 = null, $操作员 = 'admin') {
        $sql = "INSERT INTO `封禁列表表` (`封禁类型`, `封禁值`, `封禁原因`, `解封时间`, `操作员`) VALUES (?, ?, ?, ?, ?)";
        return $this->执行($sql, [$封禁类型, $封禁值, $封禁原因, $解封时间, $操作员]);
    }

    /**
     * 解除封禁
     */
    public function 解除封禁($封禁类型, $封禁值, $操作员 = 'admin') {
        $sql = "UPDATE `封禁列表表` SET `状态` = 0, `解封时间` = NOW() WHERE `封禁类型` = ? AND `封禁值` = ? AND `状态` = 1";
        return $this->执行($sql, [$封禁类型, $封禁值]);
    }

    /**
     * 封禁激活码
     */
    public function 封禁激活码($激活码, $封禁原因, $操作员 = 'admin') {
        // 开始事务
        $this->pdo->beginTransaction();

        try {
            // 更新激活码状态为封禁
            $this->执行("UPDATE `激活码表` SET `激活码状态` = 2, `封禁原因` = ?, `封禁时间` = NOW() WHERE `激活码` = ?", [$封禁原因, $激活码]);

            // 添加到封禁列表
            $this->添加封禁('激活码', $激活码, $封禁原因, null, $操作员);

            $this->pdo->commit();
            return ['success' => true, 'message' => '激活码封禁成功'];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            return ['success' => false, 'message' => '封禁失败: ' . $e->getMessage()];
        }
    }

    /**
     * 解封激活码
     */
    public function 解封激活码($激活码, $操作员 = 'admin') {
        // 开始事务
        $this->pdo->beginTransaction();

        try {
            // 更新激活码状态为正常
            $this->执行("UPDATE `激活码表` SET `激活码状态` = 1, `封禁原因` = NULL, `封禁时间` = NULL WHERE `激活码` = ?", [$激活码]);

            // 从封禁列表解除
            $this->解除封禁('激活码', $激活码, $操作员);

            $this->pdo->commit();
            return ['success' => true, 'message' => '激活码解封成功'];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            return ['success' => false, 'message' => '解封失败: ' . $e->getMessage()];
        }
    }

    /**
     * 获取激活码列表（新版本）
     */
    public function 获取激活码列表($限制 = 100, $状态过滤 = null) {
        $sql = "SELECT * FROM `激活码表`";
        $参数 = [];

        if ($状态过滤 !== null) {
            $sql .= " WHERE `激活码状态` = ?";
            $参数[] = $状态过滤;
        }

        $sql .= " ORDER BY `创建时间` DESC LIMIT ?";
        $参数[] = $限制;

        return $this->查询($sql, $参数);
    }

    /**
     * 获取激活码最新登录位置
     */
    public function 获取激活码最新登录位置($激活码) {
        try {
            // 首先尝试新版表结构（使用激活码字段）
            $sql = "SELECT `登录IP`, `ip_country`, `ip_province`, `ip_city`, `ip_area`, `ip_isp`, `登录时间`
                    FROM `登录记录表`
                    WHERE `激活码` = ? AND `登录状态` = 1
                    ORDER BY `登录时间` DESC
                    LIMIT 1";
            return $this->查询单条($sql, [$激活码]);
        } catch (Exception $e) {
            // 如果失败，尝试旧版表结构（使用验证码字段）
            try {
                $sql = "SELECT `登录IP`, `ip_country`, `ip_province`, `ip_city`, `ip_area`, `ip_isp`, `登录时间`
                        FROM `登录记录表`
                        WHERE `验证码` = ? AND `登录状态` = 1
                        ORDER BY `登录时间` DESC
                        LIMIT 1";
                return $this->查询单条($sql, [$激活码]);
            } catch (Exception $e2) {
                // 如果都失败，返回空结果
                return null;
            }
        }
    }

    /**
     * 获取点数变动记录
     */
    public function 获取点数变动记录($激活码 = null, $限制 = 100) {
        $sql = "SELECT * FROM `点数变动记录表`";
        $参数 = [];

        if ($激活码) {
            $sql .= " WHERE `激活码` = ?";
            $参数[] = $激活码;
        }

        $sql .= " ORDER BY `变动时间` DESC LIMIT ?";
        $参数[] = $限制;

        return $this->查询($sql, $参数);
    }

    /**
     * 获取封禁列表
     */
    public function 获取封禁列表($限制 = 100) {
        $sql = "SELECT * FROM `封禁列表表` WHERE `状态` = 1 ORDER BY `封禁时间` DESC LIMIT ?";
        return $this->查询($sql, [$限制]);
    }

    /**
     * 获取系统统计信息
     */
    public function 获取系统统计() {
        $统计 = [];

        // 激活码统计
        $统计['激活码总数'] = $this->查询单条("SELECT COUNT(*) as count FROM `激活码表`")['count'];
        $统计['未使用激活码'] = $this->查询单条("SELECT COUNT(*) as count FROM `激活码表` WHERE `激活码状态` = 0")['count'];
        $统计['已使用激活码'] = $this->查询单条("SELECT COUNT(*) as count FROM `激活码表` WHERE `激活码状态` = 1")['count'];
        $统计['已封禁激活码'] = $this->查询单条("SELECT COUNT(*) as count FROM `激活码表` WHERE `激活码状态` = 2")['count'];

        // 点数统计
        $点数统计 = $this->查询单条("SELECT SUM(`剩余点数`) as total_points, AVG(`剩余点数`) as avg_points FROM `激活码表`");
        $统计['总剩余点数'] = $点数统计['total_points'] ?? 0;
        $统计['平均剩余点数'] = round($点数统计['avg_points'] ?? 0, 2);

        // 登录统计
        $统计['今日登录次数'] = $this->查询单条("SELECT COUNT(*) as count FROM `登录记录表` WHERE DATE(`登录时间`) = CURDATE() AND `登录状态` = 1")['count'];
        $统计['总登录次数'] = $this->查询单条("SELECT COUNT(*) as count FROM `登录记录表` WHERE `登录状态` = 1")['count'];

        // 封禁统计
        $统计['活跃封禁数'] = $this->查询单条("SELECT COUNT(*) as count FROM `封禁列表表` WHERE `状态` = 1")['count'];

        return $统计;
    }

    /**
     * 获取系统配置
     */
    public function 获取系统配置($配置键 = null) {
        try {
            if ($配置键) {
                // 获取单个配置
                $sql = "SELECT 配置键, 配置值, 配置描述, 配置类型, 是否可编辑 FROM 系统配置表 WHERE 配置键 = ?";
                $结果 = $this->查询($sql, [$配置键]);
                return !empty($结果) ? $结果[0] : null;
            } else {
                // 获取所有配置
                $sql = "SELECT 配置键, 配置值, 配置描述, 配置类型, 是否可编辑, 更新时间 FROM 系统配置表 ORDER BY 配置键";
                return $this->查询($sql);
            }
        } catch (Exception $e) {
            throw new Exception("获取系统配置失败: " . $e->getMessage());
        }
    }

    /**
     * 更新系统配置
     */
    public function 更新系统配置($配置键, $配置值) {
        try {
            // 检查配置是否存在且可编辑
            $配置信息 = $this->获取系统配置($配置键);
            if (!$配置信息) {
                throw new Exception("配置项不存在: {$配置键}");
            }

            if (!$配置信息['是否可编辑']) {
                throw new Exception("配置项不允许编辑: {$配置键}");
            }

            // 验证配置值格式
            $this->验证配置值($配置值, $配置信息['配置类型']);

            // 更新配置
            $sql = "UPDATE 系统配置表 SET 配置值 = ?, 更新时间 = NOW() WHERE 配置键 = ?";
            $this->执行($sql, [$配置值, $配置键]);

            return [
                'success' => true,
                'message' => '配置更新成功',
                'config_key' => $配置键,
                'new_value' => $配置值
            ];

        } catch (Exception $e) {
            throw new Exception("更新系统配置失败: " . $e->getMessage());
        }
    }

    /**
     * 验证配置值格式
     */
    private function 验证配置值($配置值, $配置类型) {
        switch ($配置类型) {
            case 'number':
                if (!is_numeric($配置值)) {
                    throw new Exception("配置值必须是数字");
                }
                break;
            case 'boolean':
                if (!in_array($配置值, ['0', '1', 'true', 'false'])) {
                    throw new Exception("配置值必须是布尔值");
                }
                break;
            case 'json':
                if (json_decode($配置值) === null && json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception("配置值必须是有效的JSON格式");
                }
                break;
            case 'string':
            default:
                // 字符串类型不需要特殊验证
                break;
        }
    }

}
?>

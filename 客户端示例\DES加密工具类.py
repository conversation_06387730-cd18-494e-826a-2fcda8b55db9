#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DES加密解密工具类
提供与PHP服务端兼容的DES加密解密功能
"""

import base64
import json
import time
import hashlib
from Crypto.Cipher import DES
from Crypto.Util.Padding import pad, unpad


class DES加密工具类:
    def __init__(self, 密钥='NETAUTH2025'):
        """
        初始化DES加密工具
        :param 密钥: 8字节DES密钥
        """
        # 确保密钥长度为8字节
        self.密钥 = (密钥 + '00000000')[:8].encode('utf-8')
    
    def 加密(self, 明文):
        """
        加密数据
        :param 明文: 要加密的字符串
        :return: Base64编码的加密数据
        """
        try:
            # 创建DES加密器
            cipher = DES.new(self.密钥, DES.MODE_ECB)
            
            # 将字符串转换为字节
            if isinstance(明文, str):
                明文 = 明文.encode('utf-8')
            
            # PKCS7填充
            填充后的数据 = pad(明文, DES.block_size)
            
            # 执行加密
            密文 = cipher.encrypt(填充后的数据)
            
            # 返回Base64编码的结果
            return base64.b64encode(密文).decode('utf-8')
        except Exception as e:
            raise Exception(f"加密过程出错: {str(e)}")
    
    def 解密(self, 密文):
        """
        解密数据
        :param 密文: Base64编码的加密数据
        :return: 解密后的字符串
        """
        try:
            # Base64解码
            密文字节 = base64.b64decode(密文.encode('utf-8'))
            
            # 创建DES解密器
            cipher = DES.new(self.密钥, DES.MODE_ECB)
            
            # 执行解密
            明文字节 = cipher.decrypt(密文字节)
            
            # 移除PKCS7填充
            明文字节 = unpad(明文字节, DES.block_size)
            
            # 转换为字符串
            return 明文字节.decode('utf-8')
        except Exception as e:
            raise Exception(f"解密过程出错: {str(e)}")
    
    def 加密JSON(self, 数据):
        """
        加密JSON数据
        :param 数据: 要加密的字典数据
        :return: 加密后的字符串
        """
        json字符串 = json.dumps(数据, ensure_ascii=False, separators=(',', ':'))
        return self.加密(json字符串)
    
    def 解密JSON(self, 加密数据):
        """
        解密JSON数据
        :param 加密数据: 加密的字符串
        :return: 解密后的字典数据
        """
        json字符串 = self.解密(加密数据)
        try:
            return json.loads(json字符串)
        except json.JSONDecodeError as e:
            raise Exception(f"JSON解析失败: {str(e)}")
    
    def 生成签名(self, 数据):
        """
        生成签名
        :param 数据: 要签名的字典数据
        :return: 签名字符串
        """
        # 按键名排序
        排序后的数据 = dict(sorted(数据.items()))

        # 构建签名字符串
        签名字符串 = ''
        for 键, 值 in 排序后的数据.items():
            if 键 != 'sign':
                # 确保值是字符串类型，与PHP端保持一致
                if isinstance(值, (dict, list)):
                    值 = json.dumps(值, ensure_ascii=False, separators=(',', ':'))
                else:
                    值 = str(值)
                签名字符串 += f'{键}={值}&'

        签名字符串 = 签名字符串.rstrip('&') + self.密钥.decode('utf-8')

        return hashlib.md5(签名字符串.encode('utf-8')).hexdigest()
    
    def 验证签名(self, 数据):
        """
        验证签名
        :param 数据: 包含签名的字典数据
        :return: 签名是否有效
        """
        if 'sign' not in 数据:
            return False
        
        接收到的签名 = 数据['sign']
        计算的签名 = self.生成签名(数据)
        
        return 接收到的签名 == 计算的签名
    
    def 创建安全请求(self, 数据):
        """
        创建安全的请求数据
        :param 数据: 请求数据字典
        :return: 包含加密数据的字典
        """
        # 添加时间戳
        数据['timestamp'] = int(time.time())
        
        # 生成签名
        数据['sign'] = self.生成签名(数据)
        
        # 加密整个请求
        return {
            'encrypted_data': self.加密JSON(数据),
            'timestamp': int(time.time())
        }
    
    def 解析安全响应(self, 加密数据):
        """
        解析安全响应数据
        :param 加密数据: 加密的响应数据字符串
        :return: 解密后的数据字典
        """
        # 解密数据
        数据 = self.解密JSON(加密数据)
        
        # 验证时间戳（5分钟内有效）
        if 'timestamp' in 数据:
            时间差 = int(time.time()) - 数据['timestamp']
            if 时间差 > 300 or 时间差 < -300:
                raise Exception("响应已过期")
        
        # 验证签名（改进版本）
        try:
            if 'sign' in 数据 and not self.验证签名(数据):
                # 如果有签名但验证失败，记录警告但继续处理
                print(f"警告：签名验证失败，但继续处理响应")
        except Exception as e:
            # 签名验证异常，记录但不中断处理
            print(f"警告：签名验证异常: {e}，继续处理响应")
        
        return 数据
    
    def 测试加密解密(self):
        """
        测试加密解密功能
        """
        测试数据 = {
            'action': 'login',
            'auth_code': 'TEST001',
            'machine_code': 'TEST_MACHINE_123',
            'timestamp': int(time.time())
        }
        
        print("原始数据:", 测试数据)
        
        # 测试JSON加密解密
        加密后 = self.加密JSON(测试数据)
        print("加密后:", 加密后)
        
        解密后 = self.解密JSON(加密后)
        print("解密后:", 解密后)
        
        # 测试签名
        签名 = self.生成签名(测试数据)
        print("签名:", 签名)
        
        测试数据['sign'] = 签名
        验证结果 = self.验证签名(测试数据)
        print("签名验证:", "通过" if 验证结果 else "失败")
        
        # 测试安全请求
        安全请求 = self.创建安全请求({'action': 'test', 'data': 'hello'})
        print("安全请求:", 安全请求)


if __name__ == '__main__':
    # 运行测试
    des工具 = DES加密工具类()
    des工具.测试加密解密()

<?php
/**
 * 网络验证API v2.0 - 激活码系统
 * 支持18位激活码、点数计费、封禁系统、云数据等功能
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入依赖文件
require_once '数据库管理类.php';
require_once 'DES加密工具类.php';
require_once 'IP地理位置查询类.php';
require_once '配置文件.php';

class 新版网络验证API {
    private $数据库;
    private $加密工具;
    private $IP查询工具;
    private $客户端IP;
    
    public function __construct() {
        $this->数据库 = new 数据库管理类();
        $this->加密工具 = new DES加密工具类();
        $this->IP查询工具 = new IP地理位置查询类();
        $this->客户端IP = $this->获取客户端IP();
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private function 获取客户端IP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * 处理API请求
     */
    public function 处理请求() {
        try {
            // 记录请求日志
            $this->记录日志('INFO', 'API请求开始', [
                'ip' => $this->客户端IP,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'method' => $_SERVER['REQUEST_METHOD'] ?? ''
            ]);
            
            // 获取请求数据
            $输入数据 = file_get_contents('php://input');
            if (empty($输入数据)) {
                throw new Exception('请求数据为空');
            }
            
            $请求数据 = json_decode($输入数据, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('JSON格式错误: ' . json_last_error_msg());
            }
            
            // 检查是否为加密请求
            if (isset($请求数据['encrypted_data'])) {
                $解密数据 = $this->加密工具->解析安全请求($请求数据['encrypted_data']);
                $this->记录日志('INFO', '解密请求成功', ['action' => $解密数据['action'] ?? 'unknown']);
            } else {
                $解密数据 = $请求数据;
                $this->记录日志('INFO', '明文请求', ['action' => $解密数据['action'] ?? 'unknown']);
            }
            
            // 获取操作类型
            $操作 = $解密数据['action'] ?? '';
            
            // 路由到对应的处理方法
            switch ($操作) {
                case 'init':
                    $结果 = $this->初始化验证($解密数据);
                    break;
                case 'login':
                    $结果 = $this->处理登录($解密数据);
                    break;
                case 'verify':
                    $结果 = $this->验证状态($解密数据);
                    break;
                case 'heartbeat':
                    $结果 = $this->心跳检测($解密数据);
                    break;
                case 'query_points':
                    $结果 = $this->查询点数($解密数据);
                    break;
                case 'unbind_device':
                    $结果 = $this->设备解绑($解密数据);
                    break;
                case 'query_unbind_info':
                    $结果 = $this->查询解绑信息($解密数据);
                    break;
                case 'set_cloud_data':
                    $结果 = $this->设置云数据($解密数据);
                    break;
                case 'get_cloud_data':
                    $结果 = $this->获取云数据($解密数据);
                    break;
                case 'logout':
                    $结果 = $this->处理登出($解密数据);
                    break;
                default:
                    throw new Exception('未知的操作类型: ' . $操作);
            }
            
            // 返回加密响应
            $this->返回成功响应($结果);
            
        } catch (Exception $e) {
            $this->记录日志('ERROR', 'API请求失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->返回错误响应($e->getMessage());
        }
    }
    
    /**
     * 初始化验证 - 检查软件标识和版本号
     */
    private function 初始化验证($数据) {
        $客户端软件标识 = $数据['software_id'] ?? '';
        $客户端版本号 = $数据['software_version'] ?? '';

        // 从数据库读取最新的配置
        $服务端软件标识配置 = $this->数据库->获取系统配置('software_id');
        $服务端版本配置 = $this->数据库->获取系统配置('software_version');

        if (!$服务端软件标识配置 || !$服务端版本配置) {
            $this->记录日志('ERROR', '初始化失败-服务端配置缺失', ['ip' => $this->客户端IP]);
            throw new Exception('服务端配置异常，请联系管理员');
        }

        $服务端软件标识 = $服务端软件标识配置['配置值'];
        $服务端版本号 = $服务端版本配置['配置值'];

        // 记录初始化请求
        $this->记录日志('INFO', '客户端初始化验证', [
            'client_software_id' => $客户端软件标识,
            'client_version' => $客户端版本号,
            'server_software_id' => $服务端软件标识,
            'server_version' => $服务端版本号,
            'ip' => $this->客户端IP
        ]);

        // 检查软件标识
        if (empty($客户端软件标识)) {
            $this->记录日志('ERROR', '初始化失败-软件标识为空', ['ip' => $this->客户端IP]);
            throw new Exception('软件标识不能为空');
        }

        if ($客户端软件标识 !== $服务端软件标识) {
            $this->记录日志('ERROR', '初始化失败-软件标识不匹配', [
                'client_id' => $客户端软件标识,
                'server_id' => $服务端软件标识,
                'ip' => $this->客户端IP
            ]);
            throw new Exception('软件标识验证失败，请使用正确的客户端程序');
        }

        // 检查版本号
        if (empty($客户端版本号)) {
            $this->记录日志('ERROR', '初始化失败-版本号为空', ['ip' => $this->客户端IP]);
            throw new Exception('软件版本号不能为空');
        }

        if ($客户端版本号 !== $服务端版本号) {
            $this->记录日志('WARNING', '初始化失败-版本号不匹配', [
                'client_version' => $客户端版本号,
                'server_version' => $服务端版本号,
                'ip' => $this->客户端IP
            ]);
            throw new Exception('软件版本不匹配，当前版本：' . $客户端版本号 . '，要求版本：' . $服务端版本号 . '，请更新客户端程序');
        }

        // 初始化成功
        $this->记录日志('INFO', '客户端初始化成功', [
            'software_id' => $客户端软件标识,
            'version' => $客户端版本号,
            'ip' => $this->客户端IP
        ]);

        return [
            'status' => 'success',
            'message' => '初始化验证成功',
            'server_info' => [
                'software_id' => $服务端软件标识,
                'software_version' => $服务端版本号,
                'server_time' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 处理登录请求（新版本 - 激活码系统）
     */
    private function 处理登录($数据) {
        $激活码 = $数据['auth_code'] ?? '';
        $机器码 = $数据['machine_code'] ?? '';
        $CPU序列号 = $数据['cpu_serial'] ?? '';
        $用户代理 = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        if (empty($激活码) || empty($机器码)) {
            $this->记录登录失败($激活码, $机器码, $CPU序列号, '参数不完整', $用户代理);
            throw new Exception('激活码和机器码不能为空');
        }
        
        // 1. 检查激活码封禁状态
        $激活码封禁检查 = $this->数据库->检查激活码封禁($激活码);
        if ($激活码封禁检查['banned']) {
            $this->记录登录失败($激活码, $机器码, $CPU序列号, $激活码封禁检查['reason'], $用户代理);
            throw new Exception($激活码封禁检查['reason']);
        }
        
        // 2. 检查IP封禁状态
        $IP封禁检查 = $this->数据库->检查IP封禁($this->客户端IP);
        if ($IP封禁检查['banned']) {
            $this->记录登录失败($激活码, $机器码, $CPU序列号, $IP封禁检查['reason'], $用户代理);
            throw new Exception('IP地址已被封禁: ' . $IP封禁检查['reason']);
        }
        
        // 3. 检查机器码封禁状态
        $机器码封禁检查 = $this->数据库->检查机器码封禁($机器码);
        if ($机器码封禁检查['banned']) {
            $this->记录登录失败($激活码, $机器码, $CPU序列号, $机器码封禁检查['reason'], $用户代理);
            throw new Exception('设备已被封禁: ' . $机器码封禁检查['reason']);
        }
        
        // 4. 验证激活码
        $激活码信息 = $this->数据库->验证激活码($激活码);
        if (!$激活码信息) {
            $this->记录登录失败($激活码, $机器码, $CPU序列号, '激活码无效或已过期', $用户代理);
            throw new Exception('激活码无效或已过期');
        }
        
        // 5. 检查点数
        if ($激活码信息['剩余点数'] <= 0) {
            $this->记录登录失败($激活码, $机器码, $CPU序列号, '点数不足', $用户代理);
            throw new Exception('点数不足，无法登录');
        }
        
        // 6. 检查设备绑定
        if ($激活码信息['绑定机器码'] && $激活码信息['绑定机器码'] !== $机器码) {
            $this->记录登录失败($激活码, $机器码, $CPU序列号, '此激活码已绑定其他设备', $用户代理);
            throw new Exception('此激活码已绑定其他设备');
        }
        
        // 7. 扣除点数
        $扣除点数 = $激活码信息['每次扣除点数'] ?? 1;
        $点数扣除结果 = $this->数据库->扣除激活码点数($激活码, $扣除点数, '登录扣除', 'system', $this->客户端IP);
        
        if (!$点数扣除结果['success']) {
            $this->记录登录失败($激活码, $机器码, $CPU序列号, $点数扣除结果['error'], $用户代理);
            throw new Exception($点数扣除结果['error']);
        }
        
        // 8. 绑定设备（如果未绑定）
        if (!$激活码信息['绑定机器码']) {
            $this->数据库->更新激活码绑定($激活码, $机器码);
        }
        
        // 9. 异步查询IP地理位置信息
        $IP位置信息 = $this->查询IP地理位置($this->客户端IP);
        
        // 10. 记录登录成功（包含IP位置信息和点数信息）
        $this->数据库->记录登录日志($激活码, $this->客户端IP, $机器码, $CPU序列号, 1, null, $用户代理, $IP位置信息, $点数扣除结果);
        
        // 11. 生成会话令牌
        $会话令牌 = $this->生成会话令牌($激活码, $机器码);
        
        return [
            'status' => 'success',
            'message' => '登录成功',
            'user_info' => [
                'auth_code' => $激活码,
                'login_time' => date('Y-m-d H:i:s'),
                'remaining_points' => $点数扣除结果['after_points'],
                'total_logins' => $激活码信息['总登录次数'] + 1
            ],
            'session_token' => $会话令牌,
            'points_info' => [
                'before_points' => $点数扣除结果['before_points'],
                'deducted_points' => $点数扣除结果['deducted_points'],
                'after_points' => $点数扣除结果['after_points']
            ]
        ];
    }
    
    /**
     * 查询点数
     */
    private function 查询点数($数据) {
        $激活码 = $数据['auth_code'] ?? '';
        
        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }
        
        $激活码信息 = $this->数据库->验证激活码($激活码);
        if (!$激活码信息) {
            throw new Exception('激活码无效');
        }
        
        return [
            'status' => 'success',
            'remaining_points' => $激活码信息['剩余点数'],
            'initial_points' => $激活码信息['初始点数'],
            'total_used' => $激活码信息['初始点数'] - $激活码信息['剩余点数']
        ];
    }
    
    /**
     * 设备解绑
     */
    private function 设备解绑($数据) {
        $激活码 = $数据['auth_code'] ?? '';

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        $解绑结果 = $this->数据库->执行设备解绑($激活码);

        if (!$解绑结果['success']) {
            throw new Exception($解绑结果['message']);
        }

        return [
            'status' => 'success',
            'message' => $解绑结果['message'],
            'unbind_info' => [
                'remaining_unbinds' => $解绑结果['remaining_unbinds']
            ]
        ];
    }

    /**
     * 查询解绑信息
     */
    private function 查询解绑信息($数据) {
        $激活码 = $数据['auth_code'] ?? '';

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        // 验证激活码
        $激活码信息 = $this->数据库->验证激活码($激活码);
        if (!$激活码信息) {
            throw new Exception('激活码无效或已过期');
        }

        // 检查解绑限制
        $解绑检查 = $this->数据库->检查解绑限制($激活码);

        return [
            'status' => 'success',
            'message' => '查询成功',
            'unbind_info' => [
                'today_unbind_count' => $解绑检查['used'],
                'max_unbind_per_day' => $解绑检查['limit'],
                'remaining_unbinds' => $解绑检查['remaining'],
                'allowed' => $解绑检查['allowed'],
                'reason' => $解绑检查['reason'] ?? null,
                'reset_date' => $激活码信息['解绑重置时间'] ?? date('Y-m-d')
            ]
        ];
    }
    
    /**
     * 设置云数据
     */
    private function 设置云数据($数据) {
        $激活码 = $数据['auth_code'] ?? '';
        $云数据 = $数据['cloud_data'] ?? null;
        
        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }
        
        if ($云数据 === null) {
            throw new Exception('云数据不能为空');
        }
        
        $结果 = $this->数据库->设置云数据($激活码, $云数据);
        
        if (!$结果['success']) {
            throw new Exception('云数据保存失败');
        }
        
        return [
            'status' => 'success',
            'message' => '云数据保存成功'
        ];
    }
    
    /**
     * 获取云数据
     */
    private function 获取云数据($数据) {
        $激活码 = $数据['auth_code'] ?? '';

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        $云数据 = $this->数据库->获取云数据($激活码);

        return [
            'status' => 'success',
            'cloud_data' => $云数据
        ];
    }

    /**
     * 验证状态
     */
    private function 验证状态($数据) {
        $激活码 = $数据['auth_code'] ?? '';
        $会话令牌 = $数据['session_token'] ?? '';

        if (empty($激活码) || empty($会话令牌)) {
            throw new Exception('激活码和会话令牌不能为空');
        }

        // 验证会话令牌
        if (!$this->验证会话令牌($激活码, $会话令牌)) {
            throw new Exception('会话令牌无效');
        }

        // 检查激活码状态
        $激活码信息 = $this->数据库->验证激活码($激活码);
        if (!$激活码信息) {
            throw new Exception('激活码无效或已过期');
        }

        return [
            'status' => 'valid',
            'message' => '状态有效',
            'remaining_points' => $激活码信息['剩余点数'],
            'server_time' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 心跳检测
     */
    private function 心跳检测($数据) {
        $激活码 = $数据['auth_code'] ?? '';

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        // 1. 检查激活码封禁状态
        $激活码封禁检查 = $this->数据库->检查激活码封禁($激活码);
        if ($激活码封禁检查['banned']) {
            $this->记录日志('WARNING', '心跳检测-激活码已封禁', [
                'auth_code' => $激活码,
                'reason' => $激活码封禁检查['reason'],
                'ip' => $this->客户端IP
            ]);
            throw new Exception('激活码已被封禁: ' . $激活码封禁检查['reason']);
        }

        // 2. 检查IP封禁状态
        $IP封禁检查 = $this->数据库->检查IP封禁($this->客户端IP);
        if ($IP封禁检查['banned']) {
            $this->记录日志('WARNING', '心跳检测-IP已封禁', [
                'auth_code' => $激活码,
                'ip' => $this->客户端IP,
                'reason' => $IP封禁检查['reason']
            ]);
            throw new Exception('IP地址已被封禁: ' . $IP封禁检查['reason']);
        }

        // 3. 验证激活码有效性
        $激活码信息 = $this->数据库->验证激活码($激活码);
        if (!$激活码信息) {
            $this->记录日志('WARNING', '心跳检测-激活码无效', [
                'auth_code' => $激活码,
                'ip' => $this->客户端IP
            ]);
            throw new Exception('激活码无效或已过期');
        }

        // 4. 检查点数（可选：如果需要心跳也消耗点数）
        if ($激活码信息['剩余点数'] <= 0) {
            $this->记录日志('WARNING', '心跳检测-点数不足', [
                'auth_code' => $激活码,
                'remaining_points' => $激活码信息['剩余点数'],
                'ip' => $this->客户端IP
            ]);
            throw new Exception('点数不足，心跳检测失败');
        }

        // 5. 如果激活码已绑定设备，检查机器码是否匹配（可选）
        $机器码 = $数据['machine_code'] ?? '';
        if ($激活码信息['绑定机器码'] && !empty($机器码) && $激活码信息['绑定机器码'] !== $机器码) {
            $this->记录日志('WARNING', '心跳检测-设备不匹配', [
                'auth_code' => $激活码,
                'bound_machine' => $激活码信息['绑定机器码'],
                'current_machine' => $机器码,
                'ip' => $this->客户端IP
            ]);
            throw new Exception('设备不匹配，心跳检测失败');
        }

        // 记录成功的心跳
        $this->记录日志('INFO', '心跳检测成功', [
            'auth_code' => $激活码,
            'ip' => $this->客户端IP,
            'remaining_points' => $激活码信息['剩余点数']
        ]);

        return [
            'status' => 'alive',
            'message' => '心跳正常',
            'server_time' => date('Y-m-d H:i:s'),
            'remaining_points' => $激活码信息['剩余点数']
        ];
    }

    /**
     * 处理登出
     */
    private function 处理登出($数据) {
        $激活码 = $数据['auth_code'] ?? '';

        if (empty($激活码)) {
            throw new Exception('激活码不能为空');
        }

        return [
            'status' => 'success',
            'message' => '登出成功'
        ];
    }

    /**
     * 生成会话令牌
     */
    private function 生成会话令牌($激活码, $机器码) {
        $数据 = $激活码 . $机器码 . time() . $this->客户端IP;
        return hash('sha256', $数据);
    }

    /**
     * 验证会话令牌
     */
    private function 验证会话令牌($激活码, $会话令牌) {
        // 简单验证，实际应用中可以存储到数据库或缓存中
        return !empty($会话令牌) && strlen($会话令牌) === 64;
    }

    /**
     * 查询IP地理位置信息
     */
    private function 查询IP地理位置($ip) {
        try {
            $位置信息 = $this->IP查询工具->查询IP位置($ip);

            $this->记录日志('INFO', 'IP地理位置查询', [
                'ip' => $ip,
                'success' => $位置信息['success'],
                'location' => $this->IP查询工具->格式化位置信息($位置信息)
            ]);

            return $位置信息;

        } catch (Exception $e) {
            $this->记录日志('WARNING', 'IP地理位置查询失败', [
                'ip' => $ip,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'ip' => $ip,
                'country' => '未知',
                'province' => '未知',
                'city' => '未知',
                'area' => '未知',
                'isp' => '未知',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 记录登录失败
     */
    private function 记录登录失败($激活码, $机器码, $CPU序列号, $失败原因, $用户代理) {
        $IP位置信息 = $this->查询IP地理位置($this->客户端IP);
        $this->数据库->记录登录日志($激活码, $this->客户端IP, $机器码, $CPU序列号, 0, $失败原因, $用户代理, $IP位置信息);
    }

    /**
     * 记录日志
     */
    private function 记录日志($级别, $消息, $上下文 = []) {
        $时间戳 = date('Y-m-d H:i:s');
        $日志内容 = "[{$时间戳}] [{$级别}] {$消息}";

        if (!empty($上下文)) {
            $日志内容 .= ' ' . json_encode($上下文, JSON_UNESCAPED_UNICODE);
        }

        $日志内容 .= PHP_EOL;

        $日志目录 = __DIR__ . '/logs';
        if (!is_dir($日志目录)) {
            mkdir($日志目录, 0755, true);
        }

        $日志文件 = $日志目录 . '/' . date('Y-m-d') . '_api.log';
        file_put_contents($日志文件, $日志内容, FILE_APPEND | LOCK_EX);
    }

    /**
     * 返回成功响应
     */
    private function 返回成功响应($数据) {
        $响应 = $this->加密工具->创建安全响应($数据);
        echo json_encode($响应, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 返回错误响应
     */
    private function 返回错误响应($错误信息) {
        $错误数据 = [
            'status' => 'error',
            'message' => $错误信息,
            'timestamp' => time()
        ];

        $响应 = $this->加密工具->创建安全响应($错误数据);
        echo json_encode($响应, JSON_UNESCAPED_UNICODE);
    }
}

// 处理请求
try {
    $api = new 新版网络验证API();
    $api->处理请求();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => '服务器内部错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>

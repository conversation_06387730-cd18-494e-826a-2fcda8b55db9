<?php
/**
 * DES加密解密工具类
 * 提供数据加密和解密功能，确保传输安全
 */

class DES加密工具类 {
    private $密钥;
    private $算法 = 'DES-ECB';
    
    public function __construct($密钥 = 'NETAUTH2025') {
        // 确保密钥长度为8字节
        $this->密钥 = substr(str_pad($密钥, 8, '0'), 0, 8);
    }
    
    /**
     * 加密数据
     * @param string $明文 要加密的数据
     * @return string 加密后的Base64编码字符串
     */
    public function 加密($明文) {
        try {
            // 使用PKCS7填充
            $明文 = $this->PKCS7填充($明文, 8);
            
            // 执行DES加密
            $密文 = openssl_encrypt($明文, $this->算法, $this->密钥, OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING);
            
            if ($密文 === false) {
                throw new Exception("加密失败");
            }
            
            // 返回Base64编码的结果
            return base64_encode($密文);
        } catch (Exception $e) {
            throw new Exception("加密过程出错: " . $e->getMessage());
        }
    }
    
    /**
     * 解密数据
     * @param string $密文 Base64编码的加密数据
     * @return string 解密后的明文
     */
    public function 解密($密文) {
        try {
            // Base64解码
            $密文 = base64_decode($密文);
            if ($密文 === false) {
                throw new Exception("Base64解码失败");
            }
            
            // 执行DES解密
            $明文 = openssl_decrypt($密文, $this->算法, $this->密钥, OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING);
            
            if ($明文 === false) {
                throw new Exception("解密失败");
            }
            
            // 移除PKCS7填充
            return $this->移除PKCS7填充($明文);
        } catch (Exception $e) {
            throw new Exception("解密过程出错: " . $e->getMessage());
        }
    }
    
    /**
     * PKCS7填充
     */
    private function PKCS7填充($数据, $块大小) {
        $填充长度 = $块大小 - (strlen($数据) % $块大小);
        return $数据 . str_repeat(chr($填充长度), $填充长度);
    }
    
    /**
     * 移除PKCS7填充
     */
    private function 移除PKCS7填充($数据) {
        $填充长度 = ord($数据[strlen($数据) - 1]);
        return substr($数据, 0, strlen($数据) - $填充长度);
    }
    
    /**
     * 加密JSON数据
     * @param array $数据 要加密的数组数据
     * @return string 加密后的字符串
     */
    public function 加密JSON($数据) {
        $json字符串 = json_encode($数据, JSON_UNESCAPED_UNICODE);
        return $this->加密($json字符串);
    }
    
    /**
     * 解密JSON数据
     * @param string $加密数据 加密的字符串
     * @return array 解密后的数组数据
     */
    public function 解密JSON($加密数据) {
        $json字符串 = $this->解密($加密数据);
        $数据 = json_decode($json字符串, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON解析失败: " . json_last_error_msg());
        }
        return $数据;
    }
    
    /**
     * 生成签名
     * @param array $数据 要签名的数据
     * @return string 签名字符串
     */
    public function 生成签名($数据) {
        // 按键名排序
        ksort($数据);
        
        // 构建签名字符串
        $签名字符串 = '';
        foreach ($数据 as $键 => $值) {
            if ($键 !== 'sign') {
                // 确保值是字符串类型
                if (is_array($值) || is_object($值)) {
                    $值 = json_encode($值, JSON_UNESCAPED_UNICODE);
                } else {
                    $值 = (string)$值;
                }
                $签名字符串 .= $键 . '=' . $值 . '&';
            }
        }
        $签名字符串 = rtrim($签名字符串, '&') . $this->密钥;
        
        return md5($签名字符串);
    }
    
    /**
     * 验证签名
     * @param array $数据 包含签名的数据
     * @return bool 签名是否有效
     */
    public function 验证签名($数据) {
        if (!isset($数据['sign'])) {
            return false;
        }
        
        $接收到的签名 = $数据['sign'];
        $计算的签名 = $this->生成签名($数据);
        
        return $接收到的签名 === $计算的签名;
    }
    
    /**
     * 创建安全的响应数据
     * @param array $数据 响应数据
     * @return array 包含加密数据和签名的响应
     */
    public function 创建安全响应($数据) {
        // 添加时间戳
        $数据['timestamp'] = time();
        
        // 生成签名
        $数据['sign'] = $this->生成签名($数据);
        
        // 加密整个响应
        return [
            'encrypted_data' => $this->加密JSON($数据),
            'timestamp' => time()
        ];
    }
    
    /**
     * 解析安全请求数据
     * @param string $加密数据 加密的请求数据
     * @return array 解密后的数据
     */
    public function 解析安全请求($加密数据) {
        // 解密数据
        $数据 = $this->解密JSON($加密数据);
        
        // 验证时间戳（5分钟内有效）
        if (isset($数据['timestamp'])) {
            $时间差 = time() - $数据['timestamp'];
            if ($时间差 > 300 || $时间差 < -300) {
                throw new Exception("请求已过期");
            }
        }
        
        // 验证签名
        if (!$this->验证签名($数据)) {
            throw new Exception("签名验证失败");
        }
        
        return $数据;
    }
}
?>

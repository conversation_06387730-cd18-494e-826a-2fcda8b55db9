-- 网络验证系统数据库初始化脚本 (MySQL版本)
-- 创建时间: 2025-07-15
-- 数据库: 218_93_208_123_8

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 激活码表（升级版）
CREATE TABLE IF NOT EXISTS `激活码表` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `激活码` varchar(18) NOT NULL COMMENT '18位激活码 STEAM+13位随机',
    `激活码状态` int(11) DEFAULT 0 COMMENT '0:未使用 1:已使用 2:已封禁',
    `绑定机器码` varchar(200) DEFAULT NULL COMMENT '绑定机器码',
    `初始点数` int(11) DEFAULT 9999 COMMENT '初始点数',
    `剩余点数` int(11) DEFAULT 9999 COMMENT '剩余点数',
    `每次扣除点数` int(11) DEFAULT 1 COMMENT '每次登录扣除点数',
    `每日解绑次数` int(11) DEFAULT 0 COMMENT '当日已解绑次数',
    `解绑重置时间` date DEFAULT NULL COMMENT '解绑计数重置日期',
    `最大解绑次数` int(11) DEFAULT 3 COMMENT '每日最大解绑次数',
    `云数据` longtext COMMENT '自定义云数据JSON格式',
    `封禁原因` varchar(500) DEFAULT NULL COMMENT '封禁原因',
    `封禁时间` datetime DEFAULT NULL COMMENT '封禁时间',
    `创建时间` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `首次使用时间` datetime DEFAULT NULL COMMENT '首次使用时间',
    `最后登录时间` datetime DEFAULT NULL COMMENT '最后登录时间',
    `总登录次数` int(11) DEFAULT 0 COMMENT '总登录次数',
    `备注` text COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_激活码` (`激活码`),
    KEY `idx_激活码` (`激活码`),
    KEY `idx_机器码` (`绑定机器码`),
    KEY `idx_状态` (`激活码状态`),
    KEY `idx_点数` (`剩余点数`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='激活码表';

-- 封禁列表表
CREATE TABLE IF NOT EXISTS `封禁列表表` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `封禁类型` varchar(20) NOT NULL COMMENT '封禁类型: IP, 机器码, 激活码',
    `封禁值` varchar(200) NOT NULL COMMENT '被封禁的值',
    `封禁原因` varchar(500) NOT NULL COMMENT '封禁原因',
    `封禁时间` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '封禁时间',
    `解封时间` datetime DEFAULT NULL COMMENT '解封时间，NULL表示永久封禁',
    `操作员` varchar(100) DEFAULT 'system' COMMENT '操作员',
    `状态` int(11) DEFAULT 1 COMMENT '1:生效 0:已解封',
    PRIMARY KEY (`id`),
    KEY `idx_封禁类型` (`封禁类型`),
    KEY `idx_封禁值` (`封禁值`),
    KEY `idx_状态` (`状态`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='封禁列表表';

-- 点数变动记录表
CREATE TABLE IF NOT EXISTS `点数变动记录表` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `激活码` varchar(18) NOT NULL COMMENT '激活码',
    `变动类型` varchar(20) NOT NULL COMMENT '变动类型: 登录扣除, 手动调整, 充值',
    `变动前点数` int(11) NOT NULL COMMENT '变动前点数',
    `变动点数` int(11) NOT NULL COMMENT '变动点数（正数为增加，负数为减少）',
    `变动后点数` int(11) NOT NULL COMMENT '变动后点数',
    `变动原因` varchar(200) DEFAULT NULL COMMENT '变动原因',
    `操作员` varchar(100) DEFAULT 'system' COMMENT '操作员',
    `变动时间` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '变动时间',
    `登录IP` varchar(45) DEFAULT NULL COMMENT '操作IP',
    PRIMARY KEY (`id`),
    KEY `idx_激活码` (`激活码`),
    KEY `idx_变动类型` (`变动类型`),
    KEY `idx_变动时间` (`变动时间`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点数变动记录表';

-- 登录记录表
CREATE TABLE IF NOT EXISTS `登录记录表` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `激活码` varchar(18) NOT NULL COMMENT '激活码',
    `登录IP` varchar(45) DEFAULT NULL COMMENT '登录IP',
    `机器码` varchar(200) DEFAULT NULL COMMENT '机器码',
    `CPU序列号` varchar(100) DEFAULT NULL COMMENT 'CPU序列号',
    `登录时间` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `登录状态` int(11) DEFAULT NULL COMMENT '登录状态: 1成功 0失败',
    `失败原因` varchar(200) DEFAULT NULL COMMENT '失败原因',
    `用户代理` text COMMENT '用户代理',
    `扣除点数` int(11) DEFAULT 0 COMMENT '本次登录扣除的点数',
    `登录前点数` int(11) DEFAULT NULL COMMENT '登录前剩余点数',
    `登录后点数` int(11) DEFAULT NULL COMMENT '登录后剩余点数',
    `ip_country` varchar(50) DEFAULT NULL COMMENT 'IP所属国家',
    `ip_province` varchar(50) DEFAULT NULL COMMENT 'IP所属省份',
    `ip_city` varchar(50) DEFAULT NULL COMMENT 'IP所属城市',
    `ip_area` varchar(50) DEFAULT NULL COMMENT 'IP所属区县',
    `ip_isp` varchar(100) DEFAULT NULL COMMENT 'IP运营商',
    `ip_location_json` text COMMENT 'IP地理位置完整JSON数据',
    PRIMARY KEY (`id`),
    KEY `idx_登录时间` (`登录时间`),
    KEY `idx_登录IP` (`登录IP`),
    KEY `idx_激活码` (`激活码`),
    KEY `idx_ip_country` (`ip_country`),
    KEY `idx_ip_province` (`ip_province`),
    KEY `idx_ip_city` (`ip_city`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录记录表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `system_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_key` varchar(100) NOT NULL COMMENT '配置键',
    `config_value` text COMMENT '配置值',
    `description` varchar(200) DEFAULT NULL COMMENT '描述',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入默认配置
INSERT IGNORE INTO `系统配置表` (`配置键`, `配置值`, `描述`) VALUES
('DES_KEY', 'NETAUTH2025', 'DES加密密钥'),
('MAX_LOGIN_ATTEMPTS', '5', '最大登录尝试次数'),
('SESSION_TIMEOUT', '3600', '会话超时时间(秒)'),
('ALLOW_MULTI_DEVICE', '0', '是否允许多设备登录'),
('DEFAULT_POINTS', '100', '新激活码默认点数'),
('POINTS_PER_LOGIN', '1', '每次登录扣除点数'),
('MAX_UNBIND_PER_DAY', '3', '每日最大解绑次数'),
('ACTIVATION_CODE_PREFIX', 'STEAM', '激活码前缀');

-- 插入测试激活码数据
INSERT IGNORE INTO `激活码表` (`激活码`, `激活码状态`, `初始点数`, `剩余点数`, `备注`) VALUES
('STEAMTEST001ABC123', 0, 9999, 9999, '测试激活码1'),
('STEAMTEST002DEF456', 0, 9999, 9999, '测试激活码2'),
('STEAMDEMO123GHI789', 0, 9999, 9999, '演示激活码');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

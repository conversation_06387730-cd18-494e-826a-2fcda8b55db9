#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络验证系统简单登录示例
提供登录和解绑功能的GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import requests
import json
import threading
from datetime import datetime
from DES加密工具类 import DES加密工具类
from 硬件信息获取类 import 硬件信息获取类


class 简单登录示例:
    def __init__(self):
        self.服务器地址 = "http://222.186.21.133:8742"
        self.加密工具 = DES加密工具类()
        self.硬件工具 = 硬件信息获取类()

        # 初始化日志
        self.初始化日志()

        # 登录状态
        self.已登录 = False
        self.验证码 = None
        self.会话令牌 = None
        self.用户信息 = None
        self.机器码 = None

        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("网络验证系统 - 登录示例")
        self.root.geometry("500x400")
        self.root.resizable(False, False)

        # 配置样式
        style = ttk.Style()
        style.configure("Warning.TButton", foreground="orange")

        # 获取机器码
        self._初始化机器码()

        self.创建界面()
        self.更新界面状态()

    def 初始化日志(self):
        """初始化日志系统"""
        import logging

        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.StreamHandler(),  # 控制台输出
                logging.FileHandler('登录示例.log', encoding='utf-8')  # 文件输出
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("登录示例启动")

    def 记录日志(self, 级别, 消息, 详情=None):
        """记录日志信息"""
        try:
            日志消息 = f"{消息}"
            if 详情:
                if isinstance(详情, dict):
                    详情字符串 = ", ".join([f"{k}={v}" for k, v in 详情.items()])
                    日志消息 += f" - {详情字符串}"
                else:
                    日志消息 += f" - {详情}"

            if 级别 == "INFO":
                self.logger.info(日志消息)
            elif 级别 == "WARNING":
                self.logger.warning(日志消息)
            elif 级别 == "ERROR":
                self.logger.error(日志消息)
            elif 级别 == "DEBUG":
                self.logger.debug(日志消息)

        except Exception as e:
            print(f"日志记录失败: {e}")
    
    def _初始化机器码(self):
        """初始化机器码"""
        try:
            self.机器码 = self.硬件工具.获取机器码()
            print(f"[INFO] 机器码: {self.机器码}")
        except Exception as e:
            print(f"[ERROR] 获取机器码失败: {e}")
            self.机器码 = "UNKNOWN_MACHINE"
    
    def 创建界面(self):
        """创建主界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="网络验证系统", 
                               font=('Arial', 18, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 登录区域
        login_frame = ttk.LabelFrame(main_frame, text="用户登录", padding="15")
        login_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 验证码输入
        ttk.Label(login_frame, text="验证码:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.验证码_entry = ttk.Entry(login_frame, width=30, font=('Arial', 12))
        self.验证码_entry.grid(row=0, column=1, pady=5, padx=(10, 0))
        self.验证码_entry.insert(0, "TEST001")  # 默认测试验证码
        
        # 按钮区域
        button_container = ttk.Frame(login_frame)
        button_container.grid(row=1, column=0, columnspan=2, pady=10)

        # 登录按钮
        self.登录_button = ttk.Button(button_container, text="登录", command=self.登录)
        self.登录_button.pack(side=tk.LEFT, padx=5)

        # 自助解绑按钮
        self.自助解绑_button = ttk.Button(button_container, text="自助解绑",
                                         command=self.自助解绑, style="Warning.TButton")
        self.自助解绑_button.pack(side=tk.LEFT, padx=5)
        
        # 状态区域
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="15")
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 状态显示
        self.状态_label = ttk.Label(status_frame, text="未登录", 
                                   font=('Arial', 12), foreground='red')
        self.状态_label.pack(pady=5)
        
        self.用户信息_text = tk.Text(status_frame, height=8, width=50, 
                                    font=('Consolas', 10), state=tk.DISABLED)
        self.用户信息_text.pack(pady=5)
        
        # 操作区域
        action_frame = ttk.LabelFrame(main_frame, text="操作", padding="15")
        action_frame.pack(fill=tk.X)
        
        # 操作按钮
        button_frame = ttk.Frame(action_frame)
        button_frame.pack()
        
        self.解绑_button = ttk.Button(button_frame, text="解绑设备", 
                                     command=self.解绑设备, state=tk.DISABLED)
        self.解绑_button.pack(side=tk.LEFT, padx=5)
        
        self.登出_button = ttk.Button(button_frame, text="登出", 
                                     command=self.登出, state=tk.DISABLED)
        self.登出_button.pack(side=tk.LEFT, padx=5)
        
        self.刷新_button = ttk.Button(button_frame, text="刷新状态", 
                                     command=self.验证状态, state=tk.DISABLED)
        self.刷新_button.pack(side=tk.LEFT, padx=5)
        
        # 绑定回车键登录
        self.验证码_entry.bind('<Return>', lambda e: self.登录())

    def 自助解绑(self):
        """自助解绑功能（无需登录）"""
        # 获取输入的激活码
        激活码 = self.验证码_entry.get().strip()

        if not 激活码:
            messagebox.showwarning("警告", "请先输入激活码")
            return

        # 确认对话框
        if not messagebox.askyesno("确认解绑",
                                  f"确定要解绑激活码 {激活码} 的设备吗？\n\n"
                                  f"注意：\n"
                                  f"• 解绑后该激活码可以在新设备上使用\n"
                                  f"• 每日解绑次数有限制\n"
                                  f"• 请确保激活码输入正确\n\n"
                                  f"是否继续？"):
            return

        def 执行自助解绑():
            try:
                self.记录日志("INFO", "自助解绑开始", {"激活码": 激活码})

                # 发送解绑请求
                解绑数据 = {
                    'action': 'unbind_device',
                    'auth_code': 激活码
                }

                响应 = self.发送请求(解绑数据)

                if 响应.get('status') == 'success':
                    # 从unbind_info中获取剩余解绑次数
                    解绑信息 = 响应.get('unbind_info', {})
                    剩余解绑次数 = 解绑信息.get('remaining_unbinds', '未知')

                    成功消息 = f"激活码 {激活码} 解绑成功！\n\n"
                    if 剩余解绑次数 != '未知':
                        成功消息 += f"今日剩余解绑次数: {剩余解绑次数} 次\n"
                    成功消息 += f"现在可以在新设备上使用该激活码了。"

                    self.root.after(0, lambda: messagebox.showinfo("解绑成功", 成功消息))
                    self.记录日志("INFO", "自助解绑成功", {"激活码": 激活码, "剩余次数": 剩余解绑次数})
                else:
                    错误消息 = 响应.get('message', '解绑失败')

                    # 根据错误类型提供友好提示
                    if "激活码无效" in 错误消息:
                        友好消息 = f"激活码 {激活码} 无效或不存在，请检查输入是否正确"
                    elif "未绑定" in 错误消息:
                        友好消息 = f"激活码 {激活码} 当前未绑定任何设备，无需解绑"
                    elif "次数" in 错误消息:
                        友好消息 = f"今日解绑次数已用完，请明天再试或联系管理员"
                    elif "封禁" in 错误消息:
                        友好消息 = f"激活码 {激活码} 已被封禁，无法进行解绑操作"
                    else:
                        友好消息 = f"解绑失败：{错误消息}"

                    self.root.after(0, lambda: messagebox.showerror("解绑失败", 友好消息))
                    self.记录日志("ERROR", "自助解绑失败", {"激活码": 激活码, "错误": 错误消息})

            except Exception as e:
                错误信息 = f"解绑过程出错: {str(e)}"
                self.root.after(0, lambda: messagebox.showerror("错误", 错误信息))
                self.记录日志("ERROR", "自助解绑异常", {"激活码": 激活码, "异常": str(e)})

        # 在后台线程执行解绑
        threading.Thread(target=执行自助解绑, daemon=True).start()

    def 发送请求(self, 数据):
        """发送加密请求到服务器（增强版错误处理）"""
        操作 = 数据.get('action', 'unknown')

        try:
            self.记录日志("INFO", f"发送请求", {"操作": 操作, "服务器": self.服务器地址})

            # 创建安全请求
            安全请求 = self.加密工具.创建安全请求(数据)
            self.记录日志("DEBUG", f"加密请求数据长度", {"长度": len(str(安全请求))})

            # 发送HTTP请求（使用新版API）
            新版API地址 = f"{self.服务器地址}/新版网络验证API.php"

            self.记录日志("DEBUG", f"尝试新版API", {"URL": 新版API地址})

            # 添加重试机制
            最大重试次数 = 3
            for 重试次数 in range(最大重试次数):
                try:
                    response = requests.post(
                        新版API地址,
                        json=安全请求,
                        headers={'Content-Type': 'application/json'},
                        timeout=30
                    )
                    break
                except requests.exceptions.Timeout as e:
                    if 重试次数 < 最大重试次数 - 1:
                        self.记录日志("WARNING", f"请求超时，重试中", {"重试次数": 重试次数 + 1, "最大重试": 最大重试次数})
                        continue
                    else:
                        raise e
                except requests.exceptions.ConnectionError as e:
                    if 重试次数 < 最大重试次数 - 1:
                        self.记录日志("WARNING", f"连接错误，重试中", {"重试次数": 重试次数 + 1, "最大重试": 最大重试次数})
                        continue
                    else:
                        raise e

            self.记录日志("INFO", f"新版API响应", {"状态码": response.status_code, "响应大小": len(response.text)})

            # 详细的HTTP状态码处理
            if response.status_code == 404:
                self.记录日志("ERROR", f"API不存在", {"URL": 新版API地址})
                raise Exception("服务器API不存在，请检查服务器配置")
            elif response.status_code == 500:
                self.记录日志("ERROR", f"服务器内部错误", {"响应": response.text[:500]})
                raise Exception("服务器内部错误，请联系管理员")
            elif response.status_code == 403:
                self.记录日志("ERROR", f"访问被拒绝", {"响应": response.text[:200]})
                raise Exception("访问被拒绝，请检查权限设置")
            elif response.status_code != 200:
                self.记录日志("ERROR", f"HTTP错误", {"状态码": response.status_code, "响应": response.text[:200]})
                raise Exception(f"HTTP错误 {response.status_code}: {response.reason}")

            # 解析响应数据
            try:
                响应数据 = response.json()
                self.记录日志("DEBUG", f"响应数据解析成功", {"类型": type(响应数据).__name__})
            except json.JSONDecodeError as e:
                self.记录日志("ERROR", f"JSON解析失败", {"错误": str(e), "响应内容": response.text[:500]})
                raise Exception(f"服务器响应格式错误: {str(e)}")

            # 解析安全响应
            if 'encrypted_data' in 响应数据:
                try:
                    解密响应 = self.加密工具.解析安全响应(响应数据['encrypted_data'])
                    self.记录日志("INFO", f"响应解密成功", {"状态": 解密响应.get('status', 'unknown'), "操作": 操作})
                    return 解密响应
                except Exception as e:
                    self.记录日志("ERROR", f"响应解密失败", {"错误": str(e)})
                    raise Exception(f"响应解密失败: {str(e)}")
            else:
                self.记录日志("WARNING", f"响应未加密", {"响应": str(响应数据)[:100]})
                return 响应数据

        except requests.exceptions.Timeout as e:
            错误信息 = "网络请求超时，请检查网络连接"
            self.记录日志("ERROR", f"请求超时", {"操作": 操作, "错误": str(e)})
            raise Exception(错误信息)
        except requests.exceptions.ConnectionError as e:
            错误信息 = "无法连接到服务器，请检查网络和服务器状态"
            self.记录日志("ERROR", f"连接错误", {"操作": 操作, "错误": str(e)})
            raise Exception(错误信息)
        except requests.exceptions.RequestException as e:
            错误信息 = f"网络请求失败: {str(e)}"
            self.记录日志("ERROR", f"网络请求异常", {"操作": 操作, "错误": str(e)})
            raise Exception(错误信息)
        except Exception as e:
            # 如果是已知的业务错误，直接抛出
            if "激活码" in str(e) or "点数" in str(e) or "封禁" in str(e) or "设备" in str(e):
                self.记录日志("WARNING", f"业务逻辑错误", {"操作": 操作, "错误": str(e)})
                raise e
            else:
                错误信息 = f"请求处理失败: {str(e)}"
                self.记录日志("ERROR", f"未知错误", {"操作": 操作, "错误": str(e)})
                raise Exception(错误信息)
    
    def 登录(self):
        """用户登录"""
        验证码 = self.验证码_entry.get().strip()
        if not 验证码:
            messagebox.showerror("错误", "请输入验证码")
            return
        
        def 执行登录():
            try:
                print(f"[INFO] 开始登录，验证码: {验证码}")
                print(f"[INFO] 机器码: {self.机器码}")
                
                # 获取硬件信息
                硬件信息 = self.硬件工具.获取系统信息()
                print(f"[INFO] 硬件信息获取完成")
                
                # 构建登录请求
                登录数据 = {
                    'action': 'login',
                    'auth_code': 验证码,
                    'machine_code': self.机器码,
                    'cpu_serial': 硬件信息['cpu_serial'],
                    'mac_address': 硬件信息['mac_address'],
                    'local_ip': 硬件信息['local_ip'],
                    'public_ip': 硬件信息['public_ip']
                }
                
                print(f"[DEBUG] 登录数据准备完成")
                
                # 发送登录请求
                响应 = self.发送请求(登录数据)
                
                if 响应.get('status') == 'success':
                    # 保存登录状态
                    self.已登录 = True
                    self.验证码 = 验证码
                    self.会话令牌 = 响应.get('session_token')
                    self.用户信息 = 响应.get('user_info')

                    用户名 = self.用户信息.get('auth_code', '未知用户')
                    剩余点数 = self.用户信息.get('remaining_points', 0)
                    print(f"[SUCCESS] 登录成功: {用户名}, 剩余点数: {剩余点数}")

                    # 更新界面
                    self.root.after(0, lambda: self.登录成功回调(响应))

                else:
                    错误消息 = 响应.get('message', '未知错误')
                    print(f"[ERROR] 登录失败: {错误消息}")

                    # 根据错误类型提供不同的提示
                    if "激活码无效" in 错误消息:
                        友好消息 = "激活码无效或已过期，请检查激活码是否正确"
                    elif "点数不足" in 错误消息:
                        友好消息 = "账户点数不足，无法登录，请联系管理员充值"
                    elif "已绑定其他设备" in 错误消息:
                        友好消息 = "此激活码已绑定其他设备，请先解绑或使用正确的设备"
                    elif "封禁" in 错误消息:
                        友好消息 = f"账户或设备已被封禁：{错误消息}"
                    else:
                        友好消息 = 错误消息

                    self.root.after(0, lambda: messagebox.showerror("登录失败", 友好消息))
                    
            except Exception as e:
                print(f"[ERROR] 登录过程出错: {e}")
                self.root.after(0, lambda: messagebox.showerror("错误", f"登录失败: {str(e)}"))
        
        # 在后台线程执行登录
        threading.Thread(target=执行登录, daemon=True).start()
    
    def 登录成功回调(self, 响应):
        """登录成功后的回调（增强版）"""
        用户信息 = 响应.get('user_info', {})
        点数信息 = 响应.get('points_info', {})

        # 构建成功消息
        成功消息 = "登录成功！\n\n"
        成功消息 += f"激活码: {用户信息.get('auth_code', 'N/A')}\n"
        成功消息 += f"剩余点数: {用户信息.get('remaining_points', 0)}\n"

        if 点数信息:
            成功消息 += f"本次扣除: {点数信息.get('deducted_points', 0)} 点"

        messagebox.showinfo("登录成功", 成功消息)
        self.更新界面状态()
        self.显示用户信息(响应)
    
    def 验证状态(self):
        """验证当前登录状态"""
        if not self.已登录 or not self.验证码:
            messagebox.showwarning("警告", "请先登录")
            return
        
        def 执行验证():
            try:
                print(f"[INFO] 验证状态: {self.验证码}")
                
                验证数据 = {
                    'action': 'verify',
                    'auth_code': self.验证码,
                    'session_token': self.会话令牌
                }
                
                响应 = self.发送请求(验证数据)
                
                if 响应.get('status') == 'valid':
                    print(f"[SUCCESS] 状态验证通过")
                    self.root.after(0, lambda: messagebox.showinfo("成功", "状态验证通过"))
                else:
                    print(f"[ERROR] 状态验证失败: {响应.get('message')}")
                    self.已登录 = False
                    self.root.after(0, lambda: [
                        messagebox.showerror("验证失败", 响应.get('message')),
                        self.更新界面状态()
                    ])
                    
            except Exception as e:
                print(f"[ERROR] 状态验证出错: {e}")
                self.root.after(0, lambda: messagebox.showerror("错误", f"状态验证失败: {str(e)}"))
        
        threading.Thread(target=执行验证, daemon=True).start()
    
    def 查询解绑信息(self):
        """查询解绑次数信息"""
        if not self.已登录:
            return None

        try:
            查询数据 = {
                'action': 'query_unbind_info',
                'auth_code': self.验证码
            }

            响应 = self.发送请求(查询数据)
            return 响应

        except Exception as e:
            print(f"[ERROR] 查询解绑信息失败: {e}")
            return None

    def 解绑设备(self):
        """解绑当前设备（增强版）"""
        if not self.已登录:
            messagebox.showwarning("警告", "请先登录")
            return

        def 执行解绑查询():
            try:
                # 先查询解绑信息
                print(f"[INFO] 查询解绑信息: {self.验证码}")
                解绑信息 = self.查询解绑信息()

                if 解绑信息 and 解绑信息.get('status') == 'success':
                    解绑数据 = 解绑信息.get('unbind_info', {})
                    当前次数 = 解绑数据.get('today_unbind_count', 0)
                    最大次数 = 解绑数据.get('max_unbind_per_day', 3)
                    剩余次数 = 最大次数 - 当前次数

                    if 剩余次数 <= 0:
                        self.root.after(0, lambda: messagebox.showerror(
                            "解绑失败",
                            f"今日解绑次数已用完！\n"
                            f"今日已解绑: {当前次数}/{最大次数} 次\n"
                            f"请明天再试或联系管理员"
                        ))
                        return

                    # 显示解绑确认对话框
                    确认消息 = (f"确定要解绑当前设备吗？\n\n"
                              f"解绑信息:\n"
                              f"• 今日已解绑: {当前次数}/{最大次数} 次\n"
                              f"• 剩余解绑次数: {剩余次数} 次\n"
                              f"• 解绑后需要重新登录\n\n"
                              f"是否继续？")

                    self.root.after(0, lambda: self.显示解绑确认对话框(确认消息, 剩余次数))
                else:
                    # 如果查询失败，使用默认确认对话框
                    self.root.after(0, lambda: self.显示解绑确认对话框(
                        "确定要解绑当前设备吗？解绑后需要重新登录。", None))

            except Exception as e:
                print(f"[ERROR] 查询解绑信息出错: {e}")
                self.root.after(0, lambda: self.显示解绑确认对话框(
                    "确定要解绑当前设备吗？解绑后需要重新登录。", None))

        # 在后台线程查询解绑信息
        threading.Thread(target=执行解绑查询, daemon=True).start()

    def 显示解绑确认对话框(self, 消息, 剩余次数):
        """显示解绑确认对话框"""
        if not messagebox.askyesno("确认解绑", 消息):
            return

        def 执行解绑():
            try:
                print(f"[INFO] 解绑设备: {self.验证码}")

                # 使用新版用户API解绑
                解绑数据 = {
                    'action': 'unbind_device',
                    'auth_code': self.验证码
                }

                try:
                    response = requests.post(
                        f"{self.服务器地址}/新版网络验证API.php",
                        json=self.加密工具.创建安全请求(解绑数据),
                        headers={'Content-Type': 'application/json'},
                        timeout=30
                    )
                except:
                    # 如果新版API不可用，使用管理员API
                    print(f"[WARNING] 新版API不可用，使用管理员API解绑")
                    管理员解绑数据 = {
                        'action': 'reset_unbind_count',
                        'auth_code': self.验证码,
                        'admin_password': 'admin123456'
                    }
                    response = requests.post(
                        f"{self.服务器地址}/新版管理员API.php",
                        json=self.加密工具.创建安全请求(管理员解绑数据),
                        headers={'Content-Type': 'application/json'},
                        timeout=30
                    )

                if response.status_code == 200:
                    响应数据 = response.json()
                    if 'encrypted_data' in 响应数据:
                        响应 = self.加密工具.解析安全响应(响应数据['encrypted_data'])

                        if 响应.get('status') == 'success':
                            print(f"[SUCCESS] 设备解绑成功")
                            # 从unbind_info中获取剩余解绑次数
                            解绑信息 = 响应.get('unbind_info', {})
                            剩余解绑次数 = 解绑信息.get('remaining_unbinds', '未知')

                            成功消息 = f"设备解绑成功！\n"
                            if 剩余解绑次数 != '未知':
                                成功消息 += f"今日剩余解绑次数: {剩余解绑次数} 次"

                            self.已登录 = False
                            self.root.after(0, lambda: [
                                messagebox.showinfo("解绑成功", 成功消息),
                                self.更新界面状态()
                            ])
                        else:
                            print(f"[ERROR] 解绑失败: {响应.get('message')}")
                            self.root.after(0, lambda: messagebox.showerror("解绑失败", 响应.get('message')))
                else:
                    raise Exception(f"HTTP错误: {response.status_code}")

            except Exception as e:
                print(f"[ERROR] 解绑过程出错: {e}")
                self.root.after(0, lambda: messagebox.showerror("错误", f"解绑失败: {str(e)}"))

        # 在后台线程执行解绑
        threading.Thread(target=执行解绑, daemon=True).start()
    
    def 登出(self):
        """用户登出"""
        if not self.已登录:
            return
        
        try:
            print(f"[INFO] 用户登出: {self.验证码}")
            
            登出数据 = {
                'action': 'logout',
                'auth_code': self.验证码
            }
            
            # 发送登出请求（不等待响应）
            try:
                self.发送请求(登出数据)
            except:
                pass  # 忽略登出请求的错误
            
            # 清除状态
            self.已登录 = False
            self.验证码 = None
            self.会话令牌 = None
            self.用户信息 = None
            
            print(f"[INFO] 已登出")
            messagebox.showinfo("成功", "已登出")
            self.更新界面状态()
            
        except Exception as e:
            print(f"[ERROR] 登出过程出错: {e}")
    
    def 更新界面状态(self):
        """更新界面状态"""
        if self.已登录:
            self.状态_label.config(text="已登录", foreground='green')
            self.登录_button.config(state=tk.DISABLED)
            self.验证码_entry.config(state=tk.DISABLED)
            self.解绑_button.config(state=tk.NORMAL)
            self.登出_button.config(state=tk.NORMAL)
            self.刷新_button.config(state=tk.NORMAL)
        else:
            self.状态_label.config(text="未登录", foreground='red')
            self.登录_button.config(state=tk.NORMAL)
            self.验证码_entry.config(state=tk.NORMAL)
            self.解绑_button.config(state=tk.DISABLED)
            self.登出_button.config(state=tk.DISABLED)
            self.刷新_button.config(state=tk.DISABLED)
            
            # 清空用户信息显示
            self.用户信息_text.config(state=tk.NORMAL)
            self.用户信息_text.delete(1.0, tk.END)
            self.用户信息_text.insert(tk.END, "请先登录...")
            self.用户信息_text.config(state=tk.DISABLED)
    
    def 显示用户信息(self, 响应):
        """显示用户信息（增强版）"""
        self.用户信息_text.config(state=tk.NORMAL)
        self.用户信息_text.delete(1.0, tk.END)

        用户信息 = 响应.get('user_info', {}) if 响应 else self.用户信息 or {}
        点数信息 = 响应.get('points_info', {}) if 响应 else {}

        # 构建详细的用户信息
        信息文本 = f"""=== 登录信息 ===
激活码: {self.验证码 or 'N/A'}
登录时间: {用户信息.get('login_time', 'N/A')}
剩余点数: {用户信息.get('remaining_points', 'N/A')}
总登录次数: {用户信息.get('total_logins', 'N/A')}

=== 点数信息 ==="""

        if 点数信息:
            信息文本 += f"""
登录前点数: {点数信息.get('before_points', 'N/A')}
本次扣除: {点数信息.get('deducted_points', 'N/A')}
登录后点数: {点数信息.get('after_points', 'N/A')}"""
        else:
            信息文本 += f"""
当前点数: {用户信息.get('remaining_points', 'N/A')}"""

        信息文本 += f"""

=== 硬件信息 ===
机器码: {self.机器码 or 'N/A'}
本地IP: {self.硬件工具.获取本地IP() if hasattr(self, '硬件工具') else 'N/A'}

=== 会话信息 ===
会话令牌: {(self.会话令牌[:16] + '...') if self.会话令牌 else 'N/A'}
服务器: {self.服务器地址}
连接状态: {'在线' if self.已登录 else '离线'}
"""

        self.用户信息_text.insert(tk.END, 信息文本)
        self.用户信息_text.config(state=tk.DISABLED)
    
    def 运行(self):
        """运行登录示例"""
        print(f"[INFO] 启动网络验证登录示例")
        print(f"[INFO] 服务器地址: {self.服务器地址}")
        self.root.mainloop()


if __name__ == '__main__':
    try:
        app = 简单登录示例()
        app.运行()
    except Exception as e:
        print(f"[ERROR] 启动登录示例失败: {e}")
        input("按回车键退出...")

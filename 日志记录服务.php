<?php
/**
 * 日志记录服务端 - 独立PHP日志系统
 * 功能：接收POST请求，记录日志信息，支持DES加密
 * 特性：增强中文日志记录，专用日志目录，强壮的错误处理
 * 作者：网络验证系统
 * 版本：2.0
 */

// 错误报告设置 - 生产环境配置
error_reporting(E_ALL);
ini_set('display_errors', 0);  // 不在页面显示错误
ini_set('log_errors', 1);      // 记录错误到日志

// 系统配置参数
define('LOG_DIR', 'system_logs');                    // 专用日志存储目录
define('LOG_FILE', LOG_DIR . '/应用日志.txt');        // 主日志文件
define('DES_KEY', 'NETAUTH2024');                    // 8字节DES加密密钥
define('MAX_LOG_SIZE', 10 * 1024 * 1024);            // 单个日志文件最大10MB
define('BACKUP_COUNT', 5);                           // 保留的备份文件数量
define('LOG_ENCODING', 'UTF-8');                     // 日志文件编码

// 日志级别中文映射表
$LOG_LEVEL_NAMES = [
    'debug' => '调试信息',
    'info' => '一般信息',
    'warning' => '警告信息',
    'error' => '错误信息',
    'critical' => '严重错误'
];

// 中文字段标签映射
$CHINESE_FIELD_LABELS = [
    'timestamp' => '记录时间',
    'level' => '日志级别',
    'ip' => '客户端IP',
    'message' => '日志消息',
    'user_agent' => '用户代理',
    'extra' => '附加数据',
    'request_method' => '请求方法',
    'request_time' => '请求时间',
    'content_type' => '内容类型',
    'encrypted' => '加密传输',
    'data_size' => '数据大小',
    'processing_time' => '处理时间'
];

/**
 * 初始化专用日志目录
 * 确保system_logs目录存在，如果不存在则自动创建
 */
function init_log_directory() {
    if (!is_dir(LOG_DIR)) {
        if (!mkdir(LOG_DIR, 0755, true)) {
            error_log("无法创建专用日志目录: " . LOG_DIR);
            return false;
        }
    }
    return true;
}

/**
 * DES数据加密函数
 * 使用DES-ECB模式对数据进行加密，适用于敏感日志信息的传输保护
 */
function des_encrypt($data, $key) {
    try {
        $key = substr($key, 0, 8);  // DES密钥必须严格为8字节
        $encrypted = openssl_encrypt($data, 'DES-ECB', $key, OPENSSL_RAW_DATA);
        return base64_encode($encrypted);
    } catch (Exception $e) {
        write_error_log("DES加密失败: " . $e->getMessage());
        return false;
    }
}

/**
 * DES数据解密函数
 * 解密通过DES加密的数据，还原原始日志内容
 */
function des_decrypt($data, $key) {
    try {
        $key = substr($key, 0, 8);  // DES密钥必须严格为8字节
        $decoded = base64_decode($data);
        return openssl_decrypt($decoded, 'DES-ECB', $key, OPENSSL_RAW_DATA);
    } catch (Exception $e) {
        write_error_log("DES解密失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 写入错误日志到主日志文件
 * 所有错误信息都记录到同一个日志文件中
 */
function write_error_log($message) {
    try {
        init_log_directory();
        $timestamp = date('Y-m-d H:i:s');
        $ip = get_real_ip();
        $error_entry = "[{$timestamp}] [系统错误] [IP:{$ip}] {$message}\n";
        file_put_contents(LOG_FILE, $error_entry, FILE_APPEND | LOCK_EX);
        // 同时写入PHP系统错误日志
        error_log("日志服务错误: {$message}");
    } catch (Exception $e) {
        error_log("无法写入错误日志: " . $e->getMessage());
    }
}

/**
 * 获取客户端真实IP地址
 * 支持代理服务器和负载均衡器的IP获取，优先获取真实客户端IP
 */
function get_real_ip() {
    // 按优先级检查各种IP头信息
    $ip_headers = [
        'HTTP_X_FORWARDED_FOR',  // 代理服务器转发的真实IP
        'HTTP_X_REAL_IP',        // Nginx代理的真实IP
        'HTTP_CLIENT_IP',        // 客户端IP（某些代理）
        'REMOTE_ADDR'            // 直连IP地址
    ];

    foreach ($ip_headers as $header) {
        if (!empty($_SERVER[$header])) {
            $ips = explode(',', $_SERVER[$header]);
            $ip = trim($ips[0]);  // 取第一个IP（最原始的客户端IP）

            // 验证IP格式，排除内网和保留地址
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }

    // 如果无法获取公网IP，返回直连地址或unknown
    return $_SERVER['REMOTE_ADDR'] ?? '未知IP';
}

/**
 * 日志文件轮转机制
 * 当日志文件超过最大大小时，自动进行文件轮转，保持日志目录整洁
 */
function rotate_log_file() {
    // 确保日志目录存在
    init_log_directory();

    if (!file_exists(LOG_FILE) || filesize(LOG_FILE) < MAX_LOG_SIZE) {
        return;
    }

    try {
        // 删除最旧的备份文件
        $oldest_backup = LOG_FILE . '.' . BACKUP_COUNT;
        if (file_exists($oldest_backup)) {
            unlink($oldest_backup);
        }

        // 轮转现有备份文件
        for ($i = BACKUP_COUNT - 1; $i >= 1; $i--) {
            $old_file = LOG_FILE . '.' . $i;
            $new_file = LOG_FILE . '.' . ($i + 1);
            if (file_exists($old_file)) {
                rename($old_file, $new_file);
            }
        }

        // 当前日志文件变为第一个备份
        rename(LOG_FILE, LOG_FILE . '.1');

    } catch (Exception $e) {
        write_error_log("日志文件轮转失败: " . $e->getMessage());
    }
}

/**
 * 简化的日志写入功能
 * 一行一条日志记录，格式简洁清晰
 */
function write_log($level, $message, $extra_data = []) {
    global $LOG_LEVEL_NAMES;

    try {
        // 确保日志目录存在并进行轮转检查
        init_log_directory();
        rotate_log_file();

        // 收集基础信息
        $timestamp = date('Y-m-d H:i:s');
        $ip = get_real_ip();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '未知用户代理';
        $level_upper = strtoupper($level);
        $level_chinese = $LOG_LEVEL_NAMES[strtolower($level)] ?? '未知级别';

        // 处理附加数据
        $extra_json = !empty($extra_data) ? json_encode($extra_data, JSON_UNESCAPED_UNICODE) : '{}';

        // 生成简洁的一行日志格式
        $formatted_log = sprintf(
            "[%s] [%s-%s] [IP:%s] %s | 附加数据:%s | UA:%s\n",
            $timestamp,
            $level_upper,
            $level_chinese,
            $ip,
            $message,
            $extra_json,
            $user_agent
        );

        // 写入日志文件
        $write_result = file_put_contents(LOG_FILE, $formatted_log, FILE_APPEND | LOCK_EX);

        if ($write_result === false) {
            throw new Exception("无法写入日志文件");
        }

        return true;

    } catch (Exception $e) {
        write_error_log("日志写入失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 增强的请求参数验证
 * 提供详细的中文错误提示，帮助开发者快速定位问题
 */
function validate_request($data) {
    global $LOG_LEVEL_NAMES;

    // 检查必需字段
    $required_fields = [
        'level' => '日志级别',
        'message' => '日志消息'
    ];

    foreach ($required_fields as $field => $chinese_name) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            return "缺少必需参数: {$field} ({$chinese_name})";
        }
    }

    // 验证日志级别
    $valid_levels = array_keys($LOG_LEVEL_NAMES);
    if (!in_array(strtolower($data['level']), $valid_levels)) {
        $level_descriptions = [];
        foreach ($LOG_LEVEL_NAMES as $level => $chinese) {
            $level_descriptions[] = "{$level}({$chinese})";
        }
        return "无效的日志级别，支持的级别: " . implode(', ', $level_descriptions);
    }

    // 验证消息长度
    if (mb_strlen($data['message'], 'UTF-8') > 10000) {
        return "日志消息过长，最大支持10000个字符";
    }

    return null;
}

/**
 * 增强的POST请求处理
 * 支持标准JSON和DES加密数据，提供详细的中文错误信息
 */
function handle_post_request() {

    try {
        // 记录请求开始时间
        $request_start_time = microtime(true);

        // 获取POST请求数据
        $raw_input = file_get_contents('php://input');
        if (empty($raw_input)) {
            throw new Exception("请求体为空，请提供有效的JSON数据");
        }

        // 记录原始数据大小
        $raw_data_size = strlen($raw_input);

        // 尝试解析JSON格式数据
        $json_data = json_decode($raw_input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            // 如果不是标准JSON，尝试解析表单数据格式
            parse_str($raw_input, $json_data);
            if (empty($json_data)) {
                throw new Exception("数据格式错误，请提供有效的JSON格式数据");
            }
        }

        // 检查是否为DES加密传输的数据
        if (isset($json_data['encrypted']) && $json_data['encrypted'] === true) {
            if (!isset($json_data['data']) || empty($json_data['data'])) {
                throw new Exception("加密数据格式错误，缺少data字段");
            }

            // 执行DES解密操作
            $decrypted = des_decrypt($json_data['data'], DES_KEY);
            if ($decrypted === false) {
                throw new Exception("DES数据解密失败，请检查加密密钥是否正确");
            }

            // 解析解密后的JSON数据
            $json_data = json_decode($decrypted, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("解密后的数据不是有效的JSON格式");
            }

            // DES解密成功，继续处理
        }

        // 验证请求参数的完整性和有效性
        $validation_error = validate_request($json_data);
        if ($validation_error) {
            throw new Exception("参数验证失败: " . $validation_error);
        }

        // 提取并处理日志数据
        $level = trim($json_data['level']);
        $message = trim($json_data['message']);
        $extra_data = $json_data['extra'] ?? [];

        // 添加详细的请求上下文信息
        $processing_time = round((microtime(true) - $request_start_time) * 1000, 2);
        $extra_data = array_merge($extra_data, [
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? '未知',
            'request_time' => date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME'] ?? time()),
            'content_type' => $_SERVER['CONTENT_TYPE'] ?? '未指定',
            'data_size' => $raw_data_size . ' 字节',
            'processing_time' => $processing_time . ' 毫秒',
            'server_name' => $_SERVER['SERVER_NAME'] ?? '未知服务器',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '未知路径'
        ]);

        // 执行日志写入操作
        $success = write_log($level, $message, $extra_data);

        if ($success) {
            // 返回成功响应
            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'message' => '日志记录成功完成',
                'details' => [
                    'level' => $level,
                    'data_size' => $raw_data_size,
                    'processing_time' => $processing_time . 'ms'
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ], JSON_UNESCAPED_UNICODE);
        } else {
            throw new Exception("日志文件写入操作失败");
        }

    } catch (Exception $e) {
        // 处理所有异常情况
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage(),
            'error_code' => 'LOG_WRITE_ERROR',
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);

        // 记录详细错误信息到系统错误日志
        write_error_log("POST请求处理失败: " . $e->getMessage() . " | IP: " . get_real_ip());
    }
}

// 设置HTTP响应头 - 专用于POST日志记录服务
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');  // 只允许POST和OPTIONS
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理CORS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    echo json_encode([
        'status' => 'success',
        'message' => 'CORS预检请求处理成功',
        'allowed_methods' => ['POST', 'OPTIONS'],
        'service' => '日志记录服务',
        'version' => '2.0'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 初始化日志目录
init_log_directory();

// 根据请求方法进行路由处理
switch ($_SERVER['REQUEST_METHOD']) {
    case 'POST':
        // 处理日志记录请求
        handle_post_request();
        break;

    case 'GET':
        // GET请求不被支持，返回405错误
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => '不支持GET请求，本服务仅接受POST方式的日志记录请求',
            'error_code' => 'METHOD_NOT_ALLOWED',
            'allowed_methods' => ['POST'],
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        // GET请求被拒绝
        break;

    default:
        // 其他请求方法也不被支持
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => '不支持的请求方法: ' . $_SERVER['REQUEST_METHOD'],
            'error_code' => 'METHOD_NOT_ALLOWED',
            'allowed_methods' => ['POST'],
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        // 不支持的请求方法被拒绝
        break;
}
?>

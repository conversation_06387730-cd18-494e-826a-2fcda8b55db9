<?php
/**
 * 网络验证系统检测修复工具
 * 开发阶段：每次运行直接重建全新数据库内容
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html><html><head><meta charset='utf-8'><title>系统检测修复工具</title>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;line-height:1.6;} 
.success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;}
pre{background:#f5f5f5;padding:10px;border-radius:5px;overflow-x:auto;}
table{border-collapse:collapse;width:100%;margin:10px 0;} 
th,td{border:1px solid #ddd;padding:8px;text-align:left;} 
th{background:#f2f2f2;}
.section{background:#f0f8ff;padding:15px;margin:15px 0;border-left:4px solid #007acc;border-radius:5px;}
.fix-button{background:#28a745;color:white;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;margin:5px;}
.fix-button:hover{background:#218838;}
.rebuild{background:#dc3545;color:white;padding:15px;border-radius:5px;margin:20px 0;}
</style>";
echo "</head><body>";

echo "<h1>🔧 网络验证系统检测修复工具</h1>";
echo "<p><strong>检测时间:</strong> " . date('Y-m-d H:i:s') . "</p>";

// 开发阶段：强制重建模式
echo "<div class='rebuild'>";
echo "<h2>🚀 开发模式 - 数据库重建</h2>";
echo "<p>正在清空并重建所有数据库表...</p>";
echo "</div>";

// 数据库配置
$数据库配置 = [
    'host' => 'localhost',
    'dbname' => '218_93_208_123_8',
    'username' => '218_93_208_123_8',
    'password' => 'd13f1T1SeYwf5XnF',
    'charset' => 'utf8mb4'
];

try {
    // 连接数据库
    $dsn = "mysql:host={$数据库配置['host']};dbname={$数据库配置['dbname']};charset={$数据库配置['charset']}";
    $pdo = new PDO($dsn, $数据库配置['username'], $数据库配置['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='section'>";
    echo "<h2>📊 数据库连接检查</h2>";
    echo "<p class='success'>✅ 数据库连接: 连接正常</p>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔄 数据库重建过程</h2>";
    
    // 禁用外键检查
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // 删除所有现有表
    $现有表 = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $现有表[] = $row[0];
    }
    
    foreach ($现有表 as $表名) {
        $pdo->exec("DROP TABLE IF EXISTS `{$表名}`");
        echo "<p class='warning'>🗑️ 删除表: {$表名}</p>";
    }
    
    echo "<p class='info'>📝 开始创建新表结构...</p>";
    
    // 创建激活码表
    $激活码表SQL = "CREATE TABLE `激活码表` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `激活码` varchar(18) NOT NULL UNIQUE COMMENT '18位激活码',
        `激活码状态` int(11) DEFAULT 0 COMMENT '状态: 0未使用 1已使用 2已封禁',
        `绑定机器码` varchar(200) DEFAULT NULL COMMENT '绑定的机器码',
        `初始点数` int(11) DEFAULT 9999 COMMENT '初始点数',
        `剩余点数` int(11) DEFAULT 9999 COMMENT '剩余点数',
        `每次扣除点数` int(11) DEFAULT 1 COMMENT '每次登录扣除点数',
        `每日解绑次数` int(11) DEFAULT 0 COMMENT '每日解绑次数',
        `最大解绑次数` int(11) DEFAULT 3 COMMENT '每日最大解绑次数',
        `解绑重置时间` date DEFAULT NULL COMMENT '解绑次数重置时间',
        `云数据` longtext COMMENT '云数据存储',
        `封禁原因` varchar(500) DEFAULT NULL COMMENT '封禁原因',
        `封禁时间` datetime DEFAULT NULL COMMENT '封禁时间',
        `创建时间` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `首次使用时间` datetime DEFAULT NULL COMMENT '首次使用时间',
        `最后登录时间` datetime DEFAULT NULL COMMENT '最后登录时间',
        `总登录次数` int(11) DEFAULT 0 COMMENT '总登录次数',
        `备注` varchar(500) DEFAULT NULL COMMENT '备注信息',
        PRIMARY KEY (`id`),
        UNIQUE KEY `idx_激活码` (`激活码`),
        KEY `idx_状态` (`激活码状态`),
        KEY `idx_绑定机器码` (`绑定机器码`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='激活码表'";
    
    $pdo->exec($激活码表SQL);
    echo "<p class='success'>✅ 激活码表: 创建成功</p>";
    
    // 创建登录记录表
    $登录记录表SQL = "CREATE TABLE `登录记录表` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `激活码` varchar(18) NOT NULL COMMENT '激活码',
        `验证码` varchar(50) DEFAULT NULL COMMENT '验证码(兼容旧版)',
        `登录IP` varchar(45) DEFAULT NULL COMMENT '登录IP',
        `机器码` varchar(200) DEFAULT NULL COMMENT '机器码',
        `CPU序列号` varchar(100) DEFAULT NULL COMMENT 'CPU序列号',
        `登录时间` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
        `登录状态` int(11) DEFAULT NULL COMMENT '登录状态: 1成功 0失败',
        `失败原因` varchar(200) DEFAULT NULL COMMENT '失败原因',
        `用户代理` text COMMENT '用户代理',
        `扣除点数` int(11) DEFAULT 0 COMMENT '本次登录扣除的点数',
        `登录前点数` int(11) DEFAULT NULL COMMENT '登录前剩余点数',
        `登录后点数` int(11) DEFAULT NULL COMMENT '登录后剩余点数',
        `ip_country` varchar(50) DEFAULT NULL COMMENT 'IP所属国家',
        `ip_province` varchar(50) DEFAULT NULL COMMENT 'IP所属省份',
        `ip_city` varchar(50) DEFAULT NULL COMMENT 'IP所属城市',
        `ip_area` varchar(50) DEFAULT NULL COMMENT 'IP所属区县',
        `ip_isp` varchar(100) DEFAULT NULL COMMENT 'IP运营商',
        `ip_location_json` text COMMENT 'IP地理位置完整JSON数据',
        PRIMARY KEY (`id`),
        KEY `idx_登录时间` (`登录时间`),
        KEY `idx_登录IP` (`登录IP`),
        KEY `idx_激活码` (`激活码`),
        KEY `idx_验证码` (`验证码`),
        KEY `idx_ip_country` (`ip_country`),
        KEY `idx_ip_province` (`ip_province`),
        KEY `idx_ip_city` (`ip_city`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录记录表'";
    
    $pdo->exec($登录记录表SQL);
    echo "<p class='success'>✅ 登录记录表: 创建成功</p>";
    
    // 创建封禁列表表
    $封禁列表表SQL = "CREATE TABLE `封禁列表表` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `封禁类型` varchar(20) NOT NULL COMMENT '封禁类型: IP, 机器码, 激活码',
        `封禁值` varchar(200) NOT NULL COMMENT '被封禁的值',
        `封禁原因` varchar(500) NOT NULL COMMENT '封禁原因',
        `封禁时间` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '封禁时间',
        `解封时间` datetime DEFAULT NULL COMMENT '解封时间，NULL表示永久封禁',
        `操作员` varchar(100) DEFAULT 'system' COMMENT '操作员',
        `状态` int(11) DEFAULT 1 COMMENT '1:生效 0:已解封',
        PRIMARY KEY (`id`),
        KEY `idx_封禁类型` (`封禁类型`),
        KEY `idx_封禁值` (`封禁值`),
        KEY `idx_状态` (`状态`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='封禁列表表'";
    
    $pdo->exec($封禁列表表SQL);
    echo "<p class='success'>✅ 封禁列表表: 创建成功</p>";
    
    // 创建点数变动记录表
    $点数变动记录表SQL = "CREATE TABLE `点数变动记录表` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `激活码` varchar(18) NOT NULL COMMENT '激活码',
        `变动类型` varchar(20) NOT NULL COMMENT '变动类型: 登录扣除, 手动调整, 充值',
        `变动前点数` int(11) NOT NULL COMMENT '变动前点数',
        `变动点数` int(11) NOT NULL COMMENT '变动点数（正数为增加，负数为减少）',
        `变动后点数` int(11) NOT NULL COMMENT '变动后点数',
        `变动原因` varchar(200) DEFAULT NULL COMMENT '变动原因',
        `操作员` varchar(100) DEFAULT 'system' COMMENT '操作员',
        `变动时间` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '变动时间',
        `登录IP` varchar(45) DEFAULT NULL COMMENT '操作IP',
        PRIMARY KEY (`id`),
        KEY `idx_激活码` (`激活码`),
        KEY `idx_变动类型` (`变动类型`),
        KEY `idx_变动时间` (`变动时间`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点数变动记录表'";
    
    $pdo->exec($点数变动记录表SQL);
    echo "<p class='success'>✅ 点数变动记录表: 创建成功</p>";
    
    // 创建系统配置表
    $系统配置表SQL = "CREATE TABLE `系统配置表` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `配置键` varchar(100) NOT NULL UNIQUE COMMENT '配置键',
        `配置值` text COMMENT '配置值',
        `描述` varchar(200) DEFAULT NULL COMMENT '配置描述',
        `更新时间` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `idx_配置键` (`配置键`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表'";
    
    $pdo->exec($系统配置表SQL);
    echo "<p class='success'>✅ 系统配置表: 创建成功</p>";
    
    echo "</div>";

    // 插入初始数据
    echo "<div class='section'>";
    echo "<h2>📝 插入初始数据</h2>";

    // 插入系统配置
    $系统配置数据 = [
        ['DES_KEY', 'NETAUTH2025', 'DES加密密钥'],
        ['MAX_LOGIN_ATTEMPTS', '5', '最大登录尝试次数'],
        ['SESSION_TIMEOUT', '3600', '会话超时时间(秒)'],
        ['ALLOW_MULTI_DEVICE', '0', '是否允许多设备登录'],
        ['DEFAULT_POINTS', '100', '新激活码默认点数'],
        ['POINTS_PER_LOGIN', '1', '每次登录扣除点数'],
        ['MAX_UNBIND_PER_DAY', '3', '每日最大解绑次数'],
        ['ACTIVATION_CODE_PREFIX', 'STEAM', '激活码前缀']
    ];

    foreach ($系统配置数据 as $配置) {
        $pdo->prepare("INSERT INTO `系统配置表` (`配置键`, `配置值`, `描述`) VALUES (?, ?, ?)")
            ->execute($配置);
    }
    echo "<p class='success'>✅ 系统配置: 插入成功</p>";

    // 插入测试激活码
    $测试激活码数据 = [
        ['STEAMTEST001ABC123', 0, 9999, 9999, '测试激活码1'],
        ['STEAMTEST002DEF456', 0, 9999, 9999, '测试激活码2'],
        ['STEAMDEMO123GHI789', 0, 9999, 9999, '演示激活码'],
        ['TEST001', 0, 9999, 9999, '简单测试码'],
        ['DEMO001', 0, 9999, 9999, '演示测试码']
    ];

    foreach ($测试激活码数据 as $激活码) {
        $pdo->prepare("INSERT INTO `激活码表` (`激活码`, `激活码状态`, `初始点数`, `剩余点数`, `备注`) VALUES (?, ?, ?, ?, ?)")
            ->execute($激活码);
    }
    echo "<p class='success'>✅ 测试激活码: 插入成功</p>";

    // 恢复外键检查
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");

    echo "</div>";

    // 功能测试
    echo "<div class='section'>";
    echo "<h2>🧪 功能测试</h2>";

    // 测试激活码表查询
    $stmt = $pdo->query("SELECT COUNT(*) FROM `激活码表`");
    $激活码数量 = $stmt->fetchColumn();
    echo "<p class='success'>✅ 激活码表查询: 激活码表包含 {$激活码数量} 条记录</p>";

    echo "</div>";

    // 显示完成信息
    echo "<div class='section'>";
    echo "<h2>🎉 重建完成！</h2>";
    echo "<p class='success'>数据库已完全重建，所有表结构和初始数据已创建完成。</p>";
    echo "<p class='info'>系统现在可以正常使用。</p>";
    echo "</div>";

    // 显示总结
    echo "<div class='section'>";
    echo "<h2>📋 重建总结</h2>";
    echo "<table>";
    echo "<tr><th>项目</th><th>状态</th></tr>";
    echo "<tr><td>数据库连接</td><td class='success'>✅ 正常</td></tr>";
    echo "<tr><td>表结构重建</td><td class='success'>✅ 完成</td></tr>";
    echo "<tr><td>初始数据插入</td><td class='success'>✅ 完成</td></tr>";
    echo "<tr><td>功能测试</td><td class='success'>✅ 通过</td></tr>";
    echo "</table>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2>❌ 数据库操作失败</h2>";
    echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

echo "<p><strong>系统检测修复完成 - " . date('Y-m-d H:i:s') . "</strong></p>";
echo "</body></html>";
